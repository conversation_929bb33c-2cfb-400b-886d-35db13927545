{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "fs:allow-write-file", "fs:allow-read-file", "fs:allow-exists", "fs:allow-create", "fs:allow-mkdir", {"identifier": "fs:scope", "allow": [{"path": "$PICTURE"}, {"path": "$PICTURE/**"}, {"path": "$HOME/Pictures"}, {"path": "$HOME/Pictures/**"}, {"path": "$TEMP"}, {"path": "$TEMP/**"}]}]}