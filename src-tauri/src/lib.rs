mod screenshot;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .invoke_handler(tauri::generate_handler![
            screenshot::capture_region,
            screenshot::capture_fullscreen,
            screenshot::capture_window,
            screenshot::get_screens,
            screenshot::capture_fullscreen_preview,
            screenshot::save_screenshot_from_preview,
            screenshot::get_pictures_dir,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
