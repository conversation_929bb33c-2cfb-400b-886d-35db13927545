mod screenshot;

use tauri::Runtime;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .invoke_handler(tauri::generate_handler![
            screenshot::capture_region,
            screenshot::capture_fullscreen,
            screenshot::capture_window,
            screenshot::get_screens,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
