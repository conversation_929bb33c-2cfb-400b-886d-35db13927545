use serde::{Deserialize, Serialize};
use tauri::command;
use screenshots::Screen;

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotArea {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotResult {
    pub path: String,
    pub width: u32,
    pub height: u32,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotPreview {
    pub temp_path: String,
    pub width: u32,
    pub height: u32,
    pub suggested_filename: String,
}

#[command]
pub async fn capture_region(area: ScreenshotArea, save_path: Option<String>) -> Result<ScreenshotResult, String> {
    let screens = Screen::all().map_err(|e| format!("Failed to get screens: {}", e))?;
    
    if screens.is_empty() {
        return Err("No screens found".to_string());
    }
    
    // Use the primary screen for now
    let screen = &screens[0];

    // Capture the specified area directly
    let image = screen.capture_area(
        area.x as i32,
        area.y as i32,
        area.width as u32,
        area.height as u32,
    ).map_err(|e| format!("Failed to capture area: {}", e))?;
    
    // Generate save path
    let path = save_path.unwrap_or_else(|| generate_default_path());
    
    // Ensure directory exists
    if let Some(parent) = std::path::Path::new(&path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| format!("Failed to create directory: {}", e))?;
    }
    
    // Save the image
    image.save(&path).map_err(|e| format!("Failed to save image: {}", e))?;
    
    Ok(ScreenshotResult {
        path,
        width: area.width as u32,
        height: area.height as u32,
    })
}

#[command]
pub async fn capture_fullscreen(save_path: Option<String>) -> Result<ScreenshotResult, String> {
    let screens = Screen::all().map_err(|e| format!("Failed to get screens: {}", e))?;
    
    if screens.is_empty() {
        return Err("No screens found".to_string());
    }
    
    // Use the primary screen
    let screen = &screens[0];
    let image = screen.capture().map_err(|e| format!("Failed to capture screen: {}", e))?;
    
    // Generate save path
    let path = save_path.unwrap_or_else(|| generate_default_path());
    
    // Ensure directory exists
    if let Some(parent) = std::path::Path::new(&path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| format!("Failed to create directory: {}", e))?;
    }
    
    // Save the image directly
    image.save(&path).map_err(|e| format!("Failed to save image: {}", e))?;
    
    Ok(ScreenshotResult {
        path,
        width: image.width() as u32,
        height: image.height() as u32,
    })
}

#[command]
pub async fn capture_window(save_path: Option<String>) -> Result<ScreenshotResult, String> {
    // For now, this is the same as fullscreen
    // TODO: Implement actual window selection
    capture_fullscreen(save_path).await
}

#[command]
pub async fn capture_window_preview(window_id: u32) -> Result<ScreenshotPreview, String> {
    capture_window_by_id(window_id, None).await
}

async fn capture_window_by_id(window_id: u32, _save_path: Option<String>) -> Result<ScreenshotPreview, String> {
    #[cfg(target_os = "macos")]
    {
        // For now, fallback to fullscreen capture until we implement proper window capture
        // Window capture on macOS requires more complex Core Graphics integration
        let _ = window_id; // Suppress unused variable warning
        capture_fullscreen_preview().await
    }

    #[cfg(not(target_os = "macos"))]
    {
        // Fallback to fullscreen capture on non-macOS platforms
        let _ = window_id; // Suppress unused variable warning
        capture_fullscreen_preview().await
    }
}

#[command]
pub async fn get_screens() -> Result<Vec<ScreenInfo>, String> {
    let screens = Screen::all().map_err(|e| format!("Failed to get screens: {}", e))?;
    
    let screen_info: Vec<ScreenInfo> = screens
        .iter()
        .enumerate()
        .map(|(index, screen)| ScreenInfo {
            id: index as u32,
            width: screen.display_info.width as u32,
            height: screen.display_info.height as u32,
            x: screen.display_info.x as i32,
            y: screen.display_info.y as i32,
            is_primary: screen.display_info.is_primary,
        })
        .collect();
    
    Ok(screen_info)
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenInfo {
    pub id: u32,
    pub width: u32,
    pub height: u32,
    pub x: i32,
    pub y: i32,
    pub is_primary: bool,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct WindowInfo {
    pub id: u32,
    pub title: String,
    pub owner_name: String,
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
    pub is_minimized: bool,
}

fn generate_default_path() -> String {
    let timestamp = chrono::Local::now().format("%Y%m%d_%H%M%S");

    // Try to use Pictures directory, fallback to temp
    let pictures_dir = dirs::picture_dir()
        .unwrap_or_else(|| std::env::temp_dir());

    let mecap_dir = pictures_dir.join("Mecap");

    mecap_dir
        .join(format!("screenshot_{}.png", timestamp))
        .to_string_lossy()
        .to_string()
}

fn generate_temp_path() -> String {
    let timestamp = chrono::Local::now().format("%Y%m%d_%H%M%S_%3f");
    let temp_dir = std::env::temp_dir();

    temp_dir
        .join(format!("mecap_preview_{}.png", timestamp))
        .to_string_lossy()
        .to_string()
}

fn generate_suggested_filename() -> String {
    let timestamp = chrono::Local::now().format("%Y%m%d_%H%M%S");
    format!("screenshot_{}.png", timestamp)
}

// New preview capture commands
#[command]
pub async fn capture_fullscreen_preview() -> Result<ScreenshotPreview, String> {
    let screens = Screen::all().map_err(|e| format!("Failed to get screens: {}", e))?;

    if screens.is_empty() {
        return Err("No screens found".to_string());
    }

    // Use the primary screen
    let screen = &screens[0];
    let image = screen.capture().map_err(|e| format!("Failed to capture screen: {}", e))?;

    // Generate temporary path
    let temp_path = generate_temp_path();

    // Ensure temp directory exists
    if let Some(parent) = std::path::Path::new(&temp_path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| format!("Failed to create temp directory: {}", e))?;
    }

    // Save to temporary location
    image.save(&temp_path).map_err(|e| format!("Failed to save temp image: {}", e))?;

    Ok(ScreenshotPreview {
        temp_path,
        width: image.width() as u32,
        height: image.height() as u32,
        suggested_filename: generate_suggested_filename(),
    })
}

#[derive(Serialize, Deserialize, Debug)]
pub struct CropArea {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
}

#[command]
pub async fn save_screenshot_from_preview(temp_path: String, final_path: Option<String>, crop_area: Option<CropArea>) -> Result<ScreenshotResult, String> {
    // Verify temp file exists
    if !std::path::Path::new(&temp_path).exists() {
        return Err("Temporary screenshot file not found".to_string());
    }

    // Load the image
    let mut image = image::open(&temp_path)
        .map_err(|e| format!("Failed to read temporary image: {}", e))?;

    // Apply crop if specified
    if let Some(crop) = crop_area {
        // Validate crop area
        if crop.x + crop.width > image.width() || crop.y + crop.height > image.height() {
            return Err("Crop area exceeds image dimensions".to_string());
        }

        if crop.width == 0 || crop.height == 0 {
            return Err("Crop area must have positive dimensions".to_string());
        }

        // Crop the image
        image = image::DynamicImage::ImageRgba8(
            image.crop_imm(crop.x, crop.y, crop.width, crop.height).to_rgba8()
        );
    }

    // Generate final path
    let path = final_path.unwrap_or_else(|| generate_default_path());

    // Ensure directory exists
    if let Some(parent) = std::path::Path::new(&path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| format!("Failed to create directory: {}", e))?;
    }

    // Save the (possibly cropped) image
    image.save(&path)
        .map_err(|e| format!("Failed to save screenshot: {}", e))?;

    // Clean up temp file
    let _ = std::fs::remove_file(&temp_path);

    Ok(ScreenshotResult {
        path,
        width: image.width(),
        height: image.height(),
    })
}

#[command]
pub async fn get_pictures_dir() -> Result<String, String> {
    let pictures_dir = dirs::picture_dir()
        .ok_or_else(|| "Could not find pictures directory".to_string())?;

    Ok(pictures_dir.to_string_lossy().to_string())
}

#[command]
pub async fn list_windows() -> Result<Vec<WindowInfo>, String> {
    get_window_list()
}

#[cfg(target_os = "macos")]
fn get_window_list() -> Result<Vec<WindowInfo>, String> {
    // For now, return mock data until we implement proper Core Graphics integration
    // The Core Graphics API is complex and requires careful handling of CF types
    Ok(vec![
        WindowInfo {
            id: 1,
            title: "Safari - Google".to_string(),
            owner_name: "Safari".to_string(),
            x: 100,
            y: 100,
            width: 1200,
            height: 800,
            is_minimized: false,
        },
        WindowInfo {
            id: 2,
            title: "Visual Studio Code".to_string(),
            owner_name: "Code".to_string(),
            x: 200,
            y: 200,
            width: 1400,
            height: 900,
            is_minimized: false,
        },
        WindowInfo {
            id: 3,
            title: "Terminal".to_string(),
            owner_name: "Terminal".to_string(),
            x: 300,
            y: 300,
            width: 800,
            height: 600,
            is_minimized: false,
        },
        WindowInfo {
            id: 4,
            title: "Finder".to_string(),
            owner_name: "Finder".to_string(),
            x: 400,
            y: 400,
            width: 900,
            height: 700,
            is_minimized: false,
        },
    ])
}

#[cfg(not(target_os = "macos"))]
fn get_window_list() -> Result<Vec<WindowInfo>, String> {
    // For non-macOS platforms, return mock data for now
    Ok(vec![
        WindowInfo {
            id: 1,
            title: "Mock Window 1".to_string(),
            owner_name: "Mock App".to_string(),
            x: 100,
            y: 100,
            width: 800,
            height: 600,
            is_minimized: false,
        },
        WindowInfo {
            id: 2,
            title: "Mock Window 2".to_string(),
            owner_name: "Another App".to_string(),
            x: 200,
            y: 200,
            width: 1024,
            height: 768,
            is_minimized: false,
        },
    ])
}
