use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use tauri::{command, Runtime, Window};
use screenshots::Screen;
use image::{ImageBuffer, RgbaImage};

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotArea {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotResult {
    pub path: String,
    pub width: u32,
    pub height: u32,
}

#[command]
pub async fn capture_region(area: ScreenshotArea, save_path: Option<String>) -> Result<ScreenshotResult, String> {
    let screens = Screen::all().map_err(|e| format!("Failed to get screens: {}", e))?;
    
    if screens.is_empty() {
        return Err("No screens found".to_string());
    }
    
    // Use the primary screen for now
    let screen = &screens[0];
    
    // Capture the entire screen first
    let image = screen.capture().map_err(|e| format!("Failed to capture screen: {}", e))?;
    
    // Convert to image crate format
    let rgba_image: RgbaImage = ImageBuffer::from_raw(
        image.width() as u32,
        image.height() as u32,
        image.buffer().to_vec(),
    ).ok_or("Failed to create image buffer")?;
    
    // Crop to the specified area
    let cropped = image::imageops::crop_imm(
        &rgba_image,
        area.x as u32,
        area.y as u32,
        area.width as u32,
        area.height as u32,
    );
    
    // Generate save path
    let path = save_path.unwrap_or_else(|| generate_default_path());
    
    // Ensure directory exists
    if let Some(parent) = std::path::Path::new(&path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| format!("Failed to create directory: {}", e))?;
    }
    
    // Save the image
    cropped.to_image().save(&path).map_err(|e| format!("Failed to save image: {}", e))?;
    
    Ok(ScreenshotResult {
        path,
        width: area.width as u32,
        height: area.height as u32,
    })
}

#[command]
pub async fn capture_fullscreen(save_path: Option<String>) -> Result<ScreenshotResult, String> {
    let screens = Screen::all().map_err(|e| format!("Failed to get screens: {}", e))?;
    
    if screens.is_empty() {
        return Err("No screens found".to_string());
    }
    
    // Use the primary screen
    let screen = &screens[0];
    let image = screen.capture().map_err(|e| format!("Failed to capture screen: {}", e))?;
    
    // Generate save path
    let path = save_path.unwrap_or_else(|| generate_default_path());
    
    // Ensure directory exists
    if let Some(parent) = std::path::Path::new(&path).parent() {
        std::fs::create_dir_all(parent).map_err(|e| format!("Failed to create directory: {}", e))?;
    }
    
    // Convert and save
    let rgba_image: RgbaImage = ImageBuffer::from_raw(
        image.width() as u32,
        image.height() as u32,
        image.buffer().to_vec(),
    ).ok_or("Failed to create image buffer")?;
    
    rgba_image.save(&path).map_err(|e| format!("Failed to save image: {}", e))?;
    
    Ok(ScreenshotResult {
        path,
        width: image.width() as u32,
        height: image.height() as u32,
    })
}

#[command]
pub async fn capture_window(save_path: Option<String>) -> Result<ScreenshotResult, String> {
    // For now, this is the same as fullscreen
    // TODO: Implement actual window selection
    capture_fullscreen(save_path).await
}

#[command]
pub async fn get_screens() -> Result<Vec<ScreenInfo>, String> {
    let screens = Screen::all().map_err(|e| format!("Failed to get screens: {}", e))?;
    
    let screen_info: Vec<ScreenInfo> = screens
        .iter()
        .enumerate()
        .map(|(index, screen)| ScreenInfo {
            id: index as u32,
            width: screen.display_info.width as u32,
            height: screen.display_info.height as u32,
            x: screen.display_info.x as i32,
            y: screen.display_info.y as i32,
            is_primary: screen.display_info.is_primary,
        })
        .collect();
    
    Ok(screen_info)
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenInfo {
    pub id: u32,
    pub width: u32,
    pub height: u32,
    pub x: i32,
    pub y: i32,
    pub is_primary: bool,
}

fn generate_default_path() -> String {
    let timestamp = chrono::Local::now().format("%Y%m%d_%H%M%S");
    
    // Try to use Pictures directory, fallback to temp
    let pictures_dir = dirs::picture_dir()
        .unwrap_or_else(|| std::env::temp_dir());
    
    let mecap_dir = pictures_dir.join("Mecap");
    
    mecap_dir
        .join(format!("screenshot_{}.png", timestamp))
        .to_string_lossy()
        .to_string()
}
