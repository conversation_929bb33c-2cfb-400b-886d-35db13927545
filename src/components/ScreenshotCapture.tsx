import React from 'react';
import {
  <PERSON>ton,
  CircularProgress,
  Box,
  Card,
  CardContent,
  Typography,
  ButtonGroup,
  Tooltip,
  <PERSON>nackbar,
  Alert
} from '@mui/material';
import {
  CropFree as RegionIcon,
  Window as WindowIcon,
  Screenshot as FullScreenIcon
} from '@mui/icons-material';
import { invoke } from '@tauri-apps/api/core';
import { sendNotification, isPermissionGranted, requestPermission } from '@tauri-apps/plugin-notification';
import { useScreenshotStore } from '../store/screenshotStore';
import { RegionSelector } from './RegionSelector';
import { WindowSelector } from './WindowSelector';

interface ScreenshotResult {
  path: string;
  width: number;
  height: number;
}

interface ScreenshotArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const ScreenshotCapture = () => {
  const { isCapturing, addScreenshot, setIsCapturing } = useScreenshotStore();
  const [regionSelectorOpen, setRegionSelectorOpen] = React.useState(false);
  const [windowSelectorOpen, setWindowSelectorOpen] = React.useState(false);
  const [notification, setNotification] = React.useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({ open: false, message: '', severity: 'info' });

  const showNotification = async (message: string, severity: 'success' | 'error' | 'info' = 'info') => {
    setNotification({ open: true, message, severity });

    // Also try to send system notification
    try {
      let permissionGranted = await isPermissionGranted();
      if (!permissionGranted) {
        const permission = await requestPermission();
        permissionGranted = permission === 'granted';
      }

      if (permissionGranted) {
        await sendNotification({
          title: 'Mecap Screenshot',
          body: message,
        });
      }
    } catch (error) {
      console.log('System notification failed:', error);
    }
  };

  const captureRegion = () => {
    setRegionSelectorOpen(true);
  };

  const handleRegionCapture = async (area: ScreenshotArea) => {
    setIsCapturing(true);
    try {
      const result = await invoke<ScreenshotResult>('capture_region', {
        area,
        savePath: null
      });

      addScreenshot({
        path: result.path,
        width: result.width,
        height: result.height,
        tags: ['region'],
        name: `Region Screenshot ${new Date().toLocaleTimeString()}`
      });

      await showNotification('Region screenshot captured successfully!', 'success');

    } catch (error) {
      console.error('Screenshot failed:', error);
      await showNotification(`Screenshot failed: ${error}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  const captureWindow = () => {
    setWindowSelectorOpen(true);
  };

  const handleWindowCapture = async (windowId: string) => {
    setIsCapturing(true);
    try {
      const result = await invoke<ScreenshotResult>('capture_window', {
        savePath: null
      });

      addScreenshot({
        path: result.path,
        width: result.width,
        height: result.height,
        tags: ['window'],
        name: `Window Screenshot ${new Date().toLocaleTimeString()}`
      });

      await showNotification('Window screenshot captured successfully!', 'success');

    } catch (error) {
      console.error('Window screenshot failed:', error);
      await showNotification(`Window screenshot failed: ${error}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  const captureFullScreen = async () => {
    setIsCapturing(true);
    try {
      const result = await invoke<ScreenshotResult>('capture_fullscreen', {
        savePath: null
      });

      addScreenshot({
        path: result.path,
        width: result.width,
        height: result.height,
        tags: ['fullscreen'],
        name: `Full Screen Screenshot ${new Date().toLocaleTimeString()}`
      });

      await showNotification('Full screen screenshot captured successfully!', 'success');

    } catch (error) {
      console.error('Full screen screenshot failed:', error);
      await showNotification(`Full screen screenshot failed: ${error}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  return (
    <>
    <Card sx={{ maxWidth: 400, margin: 'auto' }}>
      <CardContent>
        <Typography variant="h5" component="h2" gutterBottom>
          Capture Screenshot
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Choose the type of screenshot you want to capture
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <ButtonGroup 
            variant="contained" 
            orientation="vertical" 
            fullWidth
            disabled={isCapturing}
          >
            <Tooltip title="Select a region of the screen to capture">
              <Button
                startIcon={isCapturing ? <CircularProgress size={20} /> : <RegionIcon />}
                onClick={captureRegion}
                sx={{ justifyContent: 'flex-start', py: 1.5 }}
              >
                Capture Region
              </Button>
            </Tooltip>
            
            <Tooltip title="Capture a specific window">
              <Button
                startIcon={isCapturing ? <CircularProgress size={20} /> : <WindowIcon />}
                onClick={captureWindow}
                sx={{ justifyContent: 'flex-start', py: 1.5 }}
              >
                Capture Window
              </Button>
            </Tooltip>
            
            <Tooltip title="Capture the entire screen">
              <Button
                startIcon={isCapturing ? <CircularProgress size={20} /> : <FullScreenIcon />}
                onClick={captureFullScreen}
                sx={{ justifyContent: 'flex-start', py: 1.5 }}
              >
                Capture Full Screen
              </Button>
            </Tooltip>
          </ButtonGroup>
        </Box>

        {isCapturing && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Capturing screenshot...
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>

    <RegionSelector
      open={regionSelectorOpen}
      onClose={() => setRegionSelectorOpen(false)}
      onConfirm={handleRegionCapture}
    />

    <WindowSelector
      open={windowSelectorOpen}
      onClose={() => setWindowSelectorOpen(false)}
      onConfirm={handleWindowCapture}
    />

    <Snackbar
      open={notification.open}
      autoHideDuration={4000}
      onClose={() => setNotification(prev => ({ ...prev, open: false }))}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
    >
      <Alert
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        severity={notification.severity}
        sx={{ width: '100%' }}
      >
        {notification.message}
      </Alert>
    </Snackbar>
  </>
  );
};
