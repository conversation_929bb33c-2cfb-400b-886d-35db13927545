import React from 'react';
import {
  <PERSON>ton,
  CircularProgress,
  Box,
  Card,
  CardContent,
  Typography,
  ButtonGroup,
  Tooltip,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Window as WindowIcon,
  Screenshot as FullScreenIcon
} from '@mui/icons-material';
import { invoke } from '@tauri-apps/api/core';
import { sendNotification, isPermissionGranted, requestPermission } from '@tauri-apps/plugin-notification';
import { useScreenshotStore } from '../store/screenshotStore';
import { WindowSelector } from './WindowSelector';
import { ScreenshotPreview } from './ScreenshotPreview';
import { convertFileSrc } from '@tauri-apps/api/core';

interface ScreenshotResult {
  path: string;
  width: number;
  height: number;
}

interface CropArea {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface ScreenshotPreviewData {
  temp_path: string;
  width: number;
  height: number;
  suggested_filename: string;
}

export const ScreenshotCapture = () => {
  const { isCapturing, addScreenshot, setIsCapturing } = useScreenshotStore();
  const [windowSelectorOpen, setWindowSelectorOpen] = React.useState(false);
  const [previewOpen, setPreviewOpen] = React.useState(false);
  const [previewData, setPreviewData] = React.useState<ScreenshotPreviewData | null>(null);
  const [captureType, setCaptureType] = React.useState<'fullscreen' | 'window'>('fullscreen');
  const [notification, setNotification] = React.useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({ open: false, message: '', severity: 'info' });

  const showNotification = async (message: string, severity: 'success' | 'error' | 'info' = 'info') => {
    setNotification({ open: true, message, severity });

    // Also try to send system notification
    try {
      let permissionGranted = await isPermissionGranted();
      if (!permissionGranted) {
        const permission = await requestPermission();
        permissionGranted = permission === 'granted';
      }

      if (permissionGranted) {
        await sendNotification({
          title: 'Mecap Screenshot',
          body: message,
        });
      }
    } catch (error) {
      console.log('System notification failed:', error);
    }
  };



  const captureWindow = () => {
    setWindowSelectorOpen(true);
  };

  const handleWindowCapture = async (windowId: string) => {
    setIsCapturing(true);
    setCaptureType('window');
    try {
      const result = await invoke<ScreenshotPreviewData>('capture_window_preview', {
        windowId: parseInt(windowId, 10)
      });
      setPreviewData(result);
      setPreviewOpen(true);
      await showNotification('Window screenshot captured! Review and save.', 'info');
    } catch (error) {
      console.error('Window screenshot failed:', error);
      await showNotification(`Window screenshot failed: ${error}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  const handlePreviewSave = async (customFilename?: string, cropArea?: CropArea) => {
    if (!previewData) return;

    try {
      setIsCapturing(true);

      // Generate final path with custom filename if provided
      let finalPath = null;
      if (customFilename) {
        const picturesDir = await invoke<string>('get_pictures_dir').catch(() => '');
        if (picturesDir) {
          finalPath = `${picturesDir}/Mecap/${customFilename}`;
        }
      }

      const result = await invoke<ScreenshotResult>('save_screenshot_from_preview', {
        tempPath: previewData.temp_path,
        finalPath,
        cropArea
      });

      addScreenshot({
        path: result.path,
        width: result.width,
        height: result.height,
        tags: [captureType],
        name: customFilename || `${captureType} Screenshot ${new Date().toLocaleTimeString()}`
      });

      await showNotification('Screenshot saved successfully!', 'success');
      setPreviewOpen(false);
      setPreviewData(null);

    } catch (error) {
      console.error('Save failed:', error);
      await showNotification(`Save failed: ${error}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  const handlePreviewEdit = () => {
    // TODO: Implement editing functionality
    showNotification('Edit functionality coming soon!', 'info');
  };

  const captureFullScreen = async () => {
    setIsCapturing(true);
    setCaptureType('fullscreen');
    try {
      const result = await invoke<ScreenshotPreviewData>('capture_fullscreen_preview');
      setPreviewData(result);
      setPreviewOpen(true);
      await showNotification('Screenshot captured! Review and save.', 'info');
    } catch (error) {
      console.error('Full screen screenshot failed:', error);
      await showNotification(`Full screen screenshot failed: ${error}`, 'error');
    } finally {
      setIsCapturing(false);
    }
  };

  return (
    <>
    <Card sx={{ maxWidth: 400, margin: 'auto' }}>
      <CardContent>
        <Typography variant="h5" component="h2" gutterBottom>
          Capture Screenshot
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Choose the type of screenshot you want to capture
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <ButtonGroup 
            variant="contained" 
            orientation="vertical" 
            fullWidth
            disabled={isCapturing}
          >

            <Tooltip title="Capture a specific window">
              <Button
                startIcon={isCapturing ? <CircularProgress size={20} /> : <WindowIcon />}
                onClick={captureWindow}
                sx={{ justifyContent: 'flex-start', py: 1.5 }}
              >
                Capture Window
              </Button>
            </Tooltip>
            
            <Tooltip title="Capture the entire screen">
              <Button
                startIcon={isCapturing ? <CircularProgress size={20} /> : <FullScreenIcon />}
                onClick={captureFullScreen}
                sx={{ justifyContent: 'flex-start', py: 1.5 }}
              >
                Capture Full Screen
              </Button>
            </Tooltip>
          </ButtonGroup>
        </Box>

        {isCapturing && (
          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Capturing screenshot...
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>

    <WindowSelector
      open={windowSelectorOpen}
      onClose={() => setWindowSelectorOpen(false)}
      onConfirm={handleWindowCapture}
    />

    <ScreenshotPreview
      open={previewOpen}
      onClose={() => {
        setPreviewOpen(false);
        setPreviewData(null);
      }}
      onSave={handlePreviewSave}
      onEdit={handlePreviewEdit}
      imagePath={previewData ? convertFileSrc(previewData.temp_path) : ''}
      captureType={captureType}
      defaultFilename={previewData?.suggested_filename || ''}
    />

    <Snackbar
      open={notification.open}
      autoHideDuration={4000}
      onClose={() => setNotification(prev => ({ ...prev, open: false }))}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
    >
      <Alert
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        severity={notification.severity}
        sx={{ width: '100%' }}
      >
        {notification.message}
      </Alert>
    </Snackbar>
  </>
  );
};
