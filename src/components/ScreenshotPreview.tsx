import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Toolbar,
  Tooltip,
  Divider,
  TextField,
  Grid
} from '@mui/material';
import {
  Close as CloseIcon,
  Save as SaveIcon,
  Edit as EditIcon,
  Crop as CropIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';

interface ScreenshotPreviewProps {
  open: boolean;
  onClose: () => void;
  onSave: (filename?: string) => void;
  onEdit: () => void;
  imagePath: string;
  captureType: 'fullscreen' | 'window' | 'region';
  defaultFilename?: string;
}

export const ScreenshotPreview: React.FC<ScreenshotPreviewProps> = ({
  open,
  onClose,
  onSave,
  onEdit,
  imagePath,
  captureType,
  defaultFilename = ''
}) => {
  const [filename, setFilename] = useState(defaultFilename);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(filename || defaultFilename);
    onClose();
  };

  const handleEdit = () => {
    setIsEditing(true);
    onEdit();
  };

  const getCaptureTypeLabel = () => {
    switch (captureType) {
      case 'fullscreen':
        return 'Full Screen Screenshot';
      case 'window':
        return 'Window Screenshot';
      case 'region':
        return 'Region Screenshot';
      default:
        return 'Screenshot';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          height: '90vh',
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ p: 0 }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {getCaptureTypeLabel()} Preview
          </Typography>
          
          <Tooltip title="Edit/Crop">
            <IconButton
              color="primary"
              onClick={handleEdit}
              disabled={isEditing}
            >
              <CropIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Close">
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Tooltip>
        </Toolbar>
        <Divider />
      </DialogTitle>

      <DialogContent sx={{ p: 2, display: 'flex', flexDirection: 'column', height: '100%' }}>
        {/* Image Preview */}
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            bgcolor: 'grey.100',
            borderRadius: 1,
            mb: 2,
            overflow: 'hidden',
            position: 'relative'
          }}
        >
          {imagePath ? (
            <img
              src={imagePath}
              alt="Screenshot preview"
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
            />
          ) : (
            <Typography color="text.secondary">
              Loading preview...
            </Typography>
          )}
          
          {isEditing && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                bgcolor: 'rgba(0, 0, 0, 0.5)',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <Typography color="white" variant="h6">
                Crop/Edit Mode (Coming Soon)
              </Typography>
            </Box>
          )}
        </Box>

        {/* Filename Input */}
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12 }}>
            <TextField
              fullWidth
              label="Filename"
              value={filename}
              onChange={(e) => setFilename(e.target.value)}
              placeholder={defaultFilename}
              helperText="Leave empty to use default filename"
              variant="outlined"
              size="small"
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 2, pt: 0 }}>
        <Button
          onClick={onClose}
          startIcon={<CancelIcon />}
          color="inherit"
        >
          Cancel
        </Button>
        
        <Button
          onClick={handleSave}
          startIcon={<SaveIcon />}
          variant="contained"
          color="primary"
        >
          Save Screenshot
        </Button>
      </DialogActions>
    </Dialog>
  );
};
