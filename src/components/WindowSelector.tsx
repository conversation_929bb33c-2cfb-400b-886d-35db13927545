import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  CircularProgress,
  Box,
} from '@mui/material';
import { invoke } from '@tauri-apps/api/core';

interface WindowInfo {
  id: number;
  title: string;
  owner_name: string;
  x: number;
  y: number;
  width: number;
  height: number;
  is_minimized: boolean;
}

interface WindowSelectorProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (windowId: string) => void;
}

export const WindowSelector: React.FC<WindowSelectorProps> = ({
  open,
  onClose,
  onConfirm,
}) => {
  const [windows, setWindows] = useState<WindowInfo[]>([]);
  const [selectedWindow, setSelectedWindow] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      loadWindows();
    }
  }, [open]);

  const loadWindows = async () => {
    setLoading(true);
    try {
      const windowList = await invoke<WindowInfo[]>('list_windows');
      setWindows(windowList);
    } catch (error) {
      console.error('Failed to load windows:', error);
      // Fallback to mock data if window enumeration fails
      const mockWindows: WindowInfo[] = [
        {
          id: 1,
          title: 'Safari - Google',
          owner_name: 'Safari',
          x: 100,
          y: 100,
          width: 1200,
          height: 800,
          is_minimized: false
        },
        {
          id: 2,
          title: 'Visual Studio Code',
          owner_name: 'Code',
          x: 200,
          y: 200,
          width: 1400,
          height: 900,
          is_minimized: false
        },
      ];
      setWindows(mockWindows);
    } finally {
      setLoading(false);
    }
  };

  const handleWindowSelect = (windowId: number) => {
    setSelectedWindow(windowId);
  };

  const handleConfirm = () => {
    if (selectedWindow !== null) {
      onConfirm(selectedWindow.toString());
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Select Window to Capture</DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Choose a window to capture as a screenshot.
        </Typography>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <List>
            {windows.map((window) => (
              <ListItem key={window.id} disablePadding>
                <ListItemButton
                  selected={selectedWindow === window.id}
                  onClick={() => handleWindowSelect(window.id)}
                >
                  <ListItemText
                    primary={window.title}
                    secondary={`${window.owner_name} • ${window.width}×${window.height}`}
                  />
                </ListItemButton>
              </ListItem>
            ))}
            {windows.length === 0 && (
              <ListItem>
                <ListItemText
                  primary="No windows found"
                  secondary="Unable to enumerate windows"
                />
              </ListItem>
            )}
          </List>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          disabled={selectedWindow === null}
        >
          Capture Window
        </Button>
      </DialogActions>
    </Dialog>
  );
};
