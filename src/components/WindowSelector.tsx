import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  CircularProgress,
  Box,
} from '@mui/material';
// import { invoke } from '@tauri-apps/api/core';

interface WindowInfo {
  id: string;
  title: string;
  process: string;
}

interface WindowSelectorProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (windowId: string) => void;
}

export const WindowSelector: React.FC<WindowSelectorProps> = ({
  open,
  onClose,
  onConfirm,
}) => {
  const [windows, setWindows] = useState<WindowInfo[]>([]);
  const [selectedWindow, setSelectedWindow] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      loadWindows();
    }
  }, [open]);

  const loadWindows = async () => {
    setLoading(true);
    try {
      // For now, we'll use a mock list since window enumeration requires platform-specific code
      // TODO: Implement actual window enumeration in Rust
      const mockWindows: WindowInfo[] = [
        { id: 'current', title: 'Current Window', process: 'Mecap' },
        { id: 'fullscreen', title: 'Full Screen', process: 'System' },
      ];
      setWindows(mockWindows);
    } catch (error) {
      console.error('Failed to load windows:', error);
      setWindows([]);
    } finally {
      setLoading(false);
    }
  };

  const handleWindowSelect = (windowId: string) => {
    setSelectedWindow(windowId);
  };

  const handleConfirm = () => {
    if (selectedWindow) {
      onConfirm(selectedWindow);
      onClose();
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Select Window to Capture</DialogTitle>
      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Choose a window to capture as a screenshot.
        </Typography>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <List>
            {windows.map((window) => (
              <ListItem key={window.id} disablePadding>
                <ListItemButton
                  selected={selectedWindow === window.id}
                  onClick={() => handleWindowSelect(window.id)}
                >
                  <ListItemText
                    primary={window.title}
                    secondary={window.process}
                  />
                </ListItemButton>
              </ListItem>
            ))}
            {windows.length === 0 && (
              <ListItem>
                <ListItemText
                  primary="No windows found"
                  secondary="Unable to enumerate windows"
                />
              </ListItem>
            )}
          </List>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={handleConfirm} 
          variant="contained"
          disabled={!selectedWindow}
        >
          Capture Window
        </Button>
      </DialogActions>
    </Dialog>
  );
};
