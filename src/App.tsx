
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Container, AppBar, Toolbar, Typography, Box } from '@mui/material';
import { theme } from './theme';
import { ScreenshotCapture } from './components/ScreenshotCapture';
import { RecentScreenshots } from './components/RecentScreenshots';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box sx={{ flexGrow: 1 }}>
        <AppBar position="static" elevation={1}>
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Mecap - Screenshot Tool
            </Typography>
          </Toolbar>
        </AppBar>

        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
            <ScreenshotCapture />
            <RecentScreenshots />
          </Box>
        </Container>
      </Box>
    </ThemeProvider>
  );
}

export default App;
