
// This file was generated by [tauri-specta](https://github.com/oscartbeaumont/tauri-specta). Do not edit this file manually.

/** user-defined commands **/


export const commands = {
async setMicInput(label: string | null) : Promise<null> {
    return await TAURI_INVOKE("set_mic_input", { label });
},
async setCameraInput(label: string | null) : Promise<boolean> {
    return await TAURI_INVOKE("set_camera_input", { label });
},
async startRecording(inputs: StartRecordingInputs) : Promise<null> {
    return await TAURI_INVOKE("start_recording", { inputs });
},
async stopRecording() : Promise<null> {
    return await TAURI_INVOKE("stop_recording");
},
async pauseRecording() : Promise<null> {
    return await TAURI_INVOKE("pause_recording");
},
async resumeRecording() : Promise<null> {
    return await TAURI_INVOKE("resume_recording");
},
async restartRecording() : Promise<null> {
    return await TAURI_INVOKE("restart_recording");
},
async deleteRecording() : Promise<null> {
    return await TAURI_INVOKE("delete_recording");
},
async listCameras() : Promise<string[]> {
    return await TAURI_INVOKE("list_cameras");
},
async listCaptureWindows() : Promise<CaptureWindow[]> {
    return await TAURI_INVOKE("list_capture_windows");
},
async listCaptureScreens() : Promise<CaptureScreen[]> {
    return await TAURI_INVOKE("list_capture_screens");
},
async takeScreenshot() : Promise<null> {
    return await TAURI_INVOKE("take_screenshot");
},
async listAudioDevices() : Promise<string[]> {
    return await TAURI_INVOKE("list_audio_devices");
},
async closeRecordingsOverlayWindow() : Promise<void> {
    await TAURI_INVOKE("close_recordings_overlay_window");
},
async setFakeWindowBounds(name: string, bounds: Bounds) : Promise<null> {
    return await TAURI_INVOKE("set_fake_window_bounds", { name, bounds });
},
async removeFakeWindow(name: string) : Promise<null> {
    return await TAURI_INVOKE("remove_fake_window", { name });
},
async focusCapturesPanel() : Promise<void> {
    await TAURI_INVOKE("focus_captures_panel");
},
async getCurrentRecording() : Promise<JsonValue<CurrentRecording | null>> {
    return await TAURI_INVOKE("get_current_recording");
},
async exportVideo(projectPath: string, progress: TAURI_CHANNEL<FramesRendered>, settings: ExportSettings) : Promise<string> {
    return await TAURI_INVOKE("export_video", { projectPath, progress, settings });
},
async getExportEstimates(path: string, resolution: XY<number>, fps: number) : Promise<ExportEstimates> {
    return await TAURI_INVOKE("get_export_estimates", { path, resolution, fps });
},
async copyFileToPath(src: string, dst: string) : Promise<null> {
    return await TAURI_INVOKE("copy_file_to_path", { src, dst });
},
async copyVideoToClipboard(path: string) : Promise<null> {
    return await TAURI_INVOKE("copy_video_to_clipboard", { path });
},
async copyScreenshotToClipboard(path: string) : Promise<null> {
    return await TAURI_INVOKE("copy_screenshot_to_clipboard", { path });
},
async openFilePath(path: string) : Promise<null> {
    return await TAURI_INVOKE("open_file_path", { path });
},
async getVideoMetadata(path: string) : Promise<VideoRecordingMetadata> {
    return await TAURI_INVOKE("get_video_metadata", { path });
},
async createEditorInstance() : Promise<SerializedEditorInstance> {
    return await TAURI_INVOKE("create_editor_instance");
},
async getMicWaveforms() : Promise<number[][]> {
    return await TAURI_INVOKE("get_mic_waveforms");
},
async getSystemAudioWaveforms() : Promise<number[][]> {
    return await TAURI_INVOKE("get_system_audio_waveforms");
},
async startPlayback(fps: number, resolutionBase: XY<number>) : Promise<null> {
    return await TAURI_INVOKE("start_playback", { fps, resolutionBase });
},
async stopPlayback() : Promise<null> {
    return await TAURI_INVOKE("stop_playback");
},
async setPlayheadPosition(frameNumber: number) : Promise<null> {
    return await TAURI_INVOKE("set_playhead_position", { frameNumber });
},
async setProjectConfig(config: ProjectConfiguration) : Promise<null> {
    return await TAURI_INVOKE("set_project_config", { config });
},
async openPermissionSettings(permission: OSPermission) : Promise<void> {
    await TAURI_INVOKE("open_permission_settings", { permission });
},
async doPermissionsCheck(initialCheck: boolean) : Promise<OSPermissionsCheck> {
    return await TAURI_INVOKE("do_permissions_check", { initialCheck });
},
async requestPermission(permission: OSPermission) : Promise<void> {
    await TAURI_INVOKE("request_permission", { permission });
},
async uploadExportedVideo(path: string, mode: UploadMode) : Promise<UploadResult> {
    return await TAURI_INVOKE("upload_exported_video", { path, mode });
},
async uploadScreenshot(screenshotPath: string) : Promise<UploadResult> {
    return await TAURI_INVOKE("upload_screenshot", { screenshotPath });
},
async getRecordingMeta(path: string, fileType: string) : Promise<RecordingMetaWithType> {
    return await TAURI_INVOKE("get_recording_meta", { path, fileType });
},
async saveFileDialog(fileName: string, fileType: string) : Promise<string | null> {
    return await TAURI_INVOKE("save_file_dialog", { fileName, fileType });
},
async listRecordings() : Promise<([string, RecordingMetaWithType])[]> {
    return await TAURI_INVOKE("list_recordings");
},
async listScreenshots() : Promise<([string, RecordingMeta])[]> {
    return await TAURI_INVOKE("list_screenshots");
},
async checkUpgradedAndUpdate() : Promise<boolean> {
    return await TAURI_INVOKE("check_upgraded_and_update");
},
async openExternalLink(url: string) : Promise<null> {
    return await TAURI_INVOKE("open_external_link", { url });
},
async setHotkey(action: HotkeyAction, hotkey: Hotkey | null) : Promise<null> {
    return await TAURI_INVOKE("set_hotkey", { action, hotkey });
},
async resetCameraPermissions() : Promise<null> {
    return await TAURI_INVOKE("reset_camera_permissions");
},
async resetMicrophonePermissions() : Promise<null> {
    return await TAURI_INVOKE("reset_microphone_permissions");
},
async isCameraWindowOpen() : Promise<boolean> {
    return await TAURI_INVOKE("is_camera_window_open");
},
async seekTo(frameNumber: number) : Promise<null> {
    return await TAURI_INVOKE("seek_to", { frameNumber });
},
async positionTrafficLights(controlsInset: [number, number] | null) : Promise<void> {
    await TAURI_INVOKE("position_traffic_lights", { controlsInset });
},
async setTheme(theme: AppTheme) : Promise<void> {
    await TAURI_INVOKE("set_theme", { theme });
},
async globalMessageDialog(message: string) : Promise<void> {
    await TAURI_INVOKE("global_message_dialog", { message });
},
async showWindow(window: ShowCapWindow) : Promise<null> {
    return await TAURI_INVOKE("show_window", { window });
},
async writeClipboardString(text: string) : Promise<null> {
    return await TAURI_INVOKE("write_clipboard_string", { text });
},
async performHapticFeedback(pattern: HapticPattern | null, time: HapticPerformanceTime | null) : Promise<null> {
    return await TAURI_INVOKE("perform_haptic_feedback", { pattern, time });
},
async listFails() : Promise<{ [key in string]: boolean }> {
    return await TAURI_INVOKE("list_fails");
},
async setFail(name: string, value: boolean) : Promise<void> {
    await TAURI_INVOKE("set_fail", { name, value });
},
async updateAuthPlan() : Promise<void> {
    await TAURI_INVOKE("update_auth_plan");
},
async setWindowTransparent(value: boolean) : Promise<void> {
    await TAURI_INVOKE("set_window_transparent", { value });
},
async getEditorMeta() : Promise<RecordingMeta> {
    return await TAURI_INVOKE("get_editor_meta");
},
async setServerUrl(serverUrl: string) : Promise<null> {
    return await TAURI_INVOKE("set_server_url", { serverUrl });
},
/**
 * Function to handle creating directories for the model
 */
async createDir(path: string, recursive: boolean) : Promise<null> {
    return await TAURI_INVOKE("create_dir", { path, recursive });
},
/**
 * Function to save the model file
 */
async saveModelFile(path: string, data: number[]) : Promise<null> {
    return await TAURI_INVOKE("save_model_file", { path, data });
},
/**
 * Function to transcribe audio from a video file using Whisper
 */
async transcribeAudio(videoPath: string, modelPath: string, language: string) : Promise<CaptionData> {
    return await TAURI_INVOKE("transcribe_audio", { videoPath, modelPath, language });
},
/**
 * Function to save caption data to a file
 */
async saveCaptions(videoId: string, captions: CaptionData) : Promise<null> {
    return await TAURI_INVOKE("save_captions", { videoId, captions });
},
/**
 * Function to load caption data from a file
 */
async loadCaptions(videoId: string) : Promise<CaptionData | null> {
    return await TAURI_INVOKE("load_captions", { videoId });
},
/**
 * Helper function to download a Whisper model from Hugging Face Hub
 */
async downloadWhisperModel(modelName: string, outputPath: string) : Promise<null> {
    return await TAURI_INVOKE("download_whisper_model", { modelName, outputPath });
},
/**
 * Function to check if a model file exists
 */
async checkModelExists(modelPath: string) : Promise<boolean> {
    return await TAURI_INVOKE("check_model_exists", { modelPath });
},
/**
 * Function to delete a downloaded model
 */
async deleteWhisperModel(modelPath: string) : Promise<null> {
    return await TAURI_INVOKE("delete_whisper_model", { modelPath });
},
/**
 * Export captions to an SRT file
 */
async exportCaptionsSrt(videoId: string) : Promise<string | null> {
    return await TAURI_INVOKE("export_captions_srt", { videoId });
}
}

/** user-defined events **/


export const events = __makeEvents__<{
audioInputLevelChange: AudioInputLevelChange,
authenticationInvalid: AuthenticationInvalid,
currentRecordingChanged: CurrentRecordingChanged,
downloadProgress: DownloadProgress,
editorStateChanged: EditorStateChanged,
newNotification: NewNotification,
newScreenshotAdded: NewScreenshotAdded,
newStudioRecordingAdded: NewStudioRecordingAdded,
recordingOptionsChanged: RecordingOptionsChanged,
recordingStarted: RecordingStarted,
recordingStopped: RecordingStopped,
renderFrameEvent: RenderFrameEvent,
requestNewScreenshot: RequestNewScreenshot,
requestOpenSettings: RequestOpenSettings,
requestStartRecording: RequestStartRecording,
uploadProgress: UploadProgress
}>({
audioInputLevelChange: "audio-input-level-change",
authenticationInvalid: "authentication-invalid",
currentRecordingChanged: "current-recording-changed",
downloadProgress: "download-progress",
editorStateChanged: "editor-state-changed",
newNotification: "new-notification",
newScreenshotAdded: "new-screenshot-added",
newStudioRecordingAdded: "new-studio-recording-added",
recordingOptionsChanged: "recording-options-changed",
recordingStarted: "recording-started",
recordingStopped: "recording-stopped",
renderFrameEvent: "render-frame-event",
requestNewScreenshot: "request-new-screenshot",
requestOpenSettings: "request-open-settings",
requestStartRecording: "request-start-recording",
uploadProgress: "upload-progress"
})

/** user-defined constants **/



/** user-defined types **/

export type AppTheme = "system" | "light" | "dark"
export type AspectRatio = "wide" | "vertical" | "square" | "classic" | "tall"
export type Audio = { duration: number; sample_rate: number; channels: number; start_time: number }
export type AudioConfiguration = { mute: boolean; improve: boolean; micVolumeDb?: number; micStereoMode?: StereoMode; systemVolumeDb?: number }
export type AudioInputLevelChange = number
export type AudioMeta = { path: string; 
/**
 * unix time of the first frame
 */
start_time?: number | null }
export type AuthSecret = { api_key: string } | { token: string; expires: number }
export type AuthStore = { secret: AuthSecret; user_id: string | null; plan: Plan | null; intercom_hash: string | null }
export type AuthenticationInvalid = null
export type BackgroundConfiguration = { source: BackgroundSource; blur: number; padding: number; rounding: number; inset: number; crop: Crop | null; shadow?: number; advancedShadow?: ShadowConfiguration | null }
export type BackgroundSource = { type: "wallpaper"; path: string | null } | { type: "image"; path: string | null } | { type: "color"; value: [number, number, number] } | { type: "gradient"; from: [number, number, number]; to: [number, number, number]; angle?: number }
export type Bounds = { x: number; y: number; width: number; height: number }
export type Camera = { hide: boolean; mirror: boolean; position: CameraPosition; size: number; zoom_size: number | null; rounding?: number; shadow?: number; advanced_shadow?: ShadowConfiguration | null; shape?: CameraShape }
export type CameraPosition = { x: CameraXPosition; y: CameraYPosition }
export type CameraShape = "square" | "source"
export type CameraXPosition = "left" | "center" | "right"
export type CameraYPosition = "top" | "bottom"
export type CaptionData = { segments: CaptionSegment[]; settings: CaptionSettings | null }
export type CaptionSegment = { id: string; start: number; end: number; text: string }
export type CaptionSettings = { enabled: boolean; font: string; size: number; color: string; backgroundColor: string; backgroundOpacity: number; position: string; bold: boolean; italic: boolean; outline: boolean; outlineColor: string; exportWithSubtitles: boolean }
export type CaptionsData = { segments: CaptionSegment[]; settings: CaptionSettings }
export type CaptureScreen = { id: number; name: string; refresh_rate: number }
export type CaptureWindow = { id: number; owner_name: string; name: string; bounds: Bounds; refresh_rate: number }
export type CommercialLicense = { licenseKey: string; expiryDate: number | null; refresh: number; activatedOn: number }
export type Crop = { position: XY<number>; size: XY<number> }
export type CurrentRecording = { target: CurrentRecordingTarget; type: RecordingType }
export type CurrentRecordingChanged = null
export type CurrentRecordingTarget = { window: { id: number; bounds: Bounds } } | { screen: { id: number } } | { area: { screen: number; bounds: Bounds } }
export type CursorAnimationStyle = "regular" | "slow" | "fast"
export type CursorConfiguration = { hide?: boolean; hideWhenIdle: boolean; size: number; type: CursorType; animationStyle: CursorAnimationStyle; tension: number; mass: number; friction: number; raw?: boolean; motionBlur?: number }
export type CursorMeta = { imagePath: string; hotspot: XY<number> }
export type CursorType = "pointer" | "circle"
export type Cursors = { [key in string]: string } | { [key in string]: CursorMeta }
export type DownloadProgress = { progress: number; message: string }
export type EditorStateChanged = { playhead_position: number }
export type ExportCompression = "Minimal" | "Social" | "Web" | "Potato"
export type ExportEstimates = { duration_seconds: number; estimated_time_seconds: number; estimated_size_mb: number }
export type ExportSettings = ({ format: "Mp4" } & Mp4ExportSettings) | ({ format: "Gif" } & GifExportSettings)
export type Flags = { captions: boolean }
export type FramesRendered = { renderedCount: number; totalFrames: number; type: "FramesRendered" }
export type GeneralSettingsStore = { instanceId?: string; uploadIndividualFiles?: boolean; hideDockIcon?: boolean; hapticsEnabled?: boolean; autoCreateShareableLink?: boolean; enableNotifications?: boolean; disableAutoOpenLinks?: boolean; hasCompletedStartup?: boolean; theme?: AppTheme; commercialLicense?: CommercialLicense | null; lastVersion?: string | null; windowTransparency?: boolean; postStudioRecordingBehaviour?: PostStudioRecordingBehaviour; mainWindowRecordingStartBehaviour?: MainWindowRecordingStartBehaviour; customCursorCapture?: boolean; serverUrl?: string; 
/**
 * @deprecated
 */
openEditorAfterRecording?: boolean }
export type GifExportSettings = { fps: number; resolution_base: XY<number> }
export type HapticPattern = "Alignment" | "LevelChange" | "Generic"
export type HapticPerformanceTime = "Default" | "Now" | "DrawCompleted"
export type Hotkey = { code: string; meta: boolean; ctrl: boolean; alt: boolean; shift: boolean }
export type HotkeyAction = "startRecording" | "stopRecording" | "restartRecording"
export type HotkeysConfiguration = { show: boolean }
export type HotkeysStore = { hotkeys: { [key in HotkeyAction]: Hotkey } }
export type InstantRecordingMeta = { fps: number; sample_rate: number | null }
export type JsonValue<T> = [T]
export type MainWindowRecordingStartBehaviour = "close" | "minimise"
export type Mp4ExportSettings = { fps: number; resolution_base: XY<number>; compression: ExportCompression }
export type MultipleSegment = { display: VideoMeta; camera?: VideoMeta | null; mic?: AudioMeta | null; system_audio?: AudioMeta | null; cursor?: string | null }
export type MultipleSegments = { segments: MultipleSegment[]; cursors: Cursors }
export type NewNotification = { title: string; body: string; is_error: boolean }
export type NewScreenshotAdded = { path: string }
export type NewStudioRecordingAdded = { path: string }
export type OSPermission = "screenRecording" | "camera" | "microphone" | "accessibility"
export type OSPermissionStatus = "notNeeded" | "empty" | "granted" | "denied"
export type OSPermissionsCheck = { screenRecording: OSPermissionStatus; microphone: OSPermissionStatus; camera: OSPermissionStatus; accessibility: OSPermissionStatus }
export type Plan = { upgraded: boolean; manual: boolean; last_checked: number }
export type Platform = "MacOS" | "Windows"
export type PostStudioRecordingBehaviour = "openEditor" | "showOverlay"
export type Preset = { name: string; config: ProjectConfiguration }
export type PresetsStore = { presets: Preset[]; default: number | null }
export type ProjectConfiguration = { aspectRatio: AspectRatio | null; background: BackgroundConfiguration; camera: Camera; audio: AudioConfiguration; cursor: CursorConfiguration; hotkeys: HotkeysConfiguration; timeline?: TimelineConfiguration | null; captions?: CaptionsData | null }
export type ProjectRecordingsMeta = { segments: SegmentRecordings[] }
export type RecordingMeta = (StudioRecordingMeta | InstantRecordingMeta) & { platform: Platform | null; pretty_name: string; sharing?: SharingMeta | null }
export type RecordingMetaWithType = ((StudioRecordingMeta | InstantRecordingMeta) & { platform: Platform | null; pretty_name: string; sharing?: SharingMeta | null }) & { type: RecordingType }
export type RecordingMode = "studio" | "instant"
export type RecordingOptionsChanged = null
export type RecordingStarted = null
export type RecordingStopped = null
export type RecordingType = "studio" | "instant"
export type RenderFrameEvent = { frame_number: number; fps: number; resolution_base: XY<number> }
export type RequestNewScreenshot = null
export type RequestOpenSettings = { page: string }
export type RequestStartRecording = null
export type S3UploadMeta = { id: string }
export type ScreenCaptureTarget = { variant: "window"; id: number } | { variant: "screen"; id: number } | { variant: "area"; screen: number; bounds: Bounds }
export type SegmentRecordings = { display: Video; camera: Video | null; mic: Audio | null; system_audio: Audio | null }
export type SerializedEditorInstance = { framesSocketUrl: string; recordingDuration: number; savedProjectConfig: ProjectConfiguration; recordings: ProjectRecordingsMeta; path: string }
export type ShadowConfiguration = { size: number; opacity: number; blur: number }
export type SharingMeta = { id: string; link: string }
export type ShowCapWindow = "Setup" | "Main" | { Settings: { page: string | null } } | { Editor: { project_path: string } } | "RecordingsOverlay" | { WindowCaptureOccluder: { screen_id: number } } | { CaptureArea: { screen_id: number } } | "Camera" | { InProgressRecording: { position: [number, number] | null } } | "Upgrade" | "ModeSelect"
export type SingleSegment = { display: VideoMeta; camera?: VideoMeta | null; audio?: AudioMeta | null; cursor?: string | null }
export type StartRecordingInputs = { capture_target: ScreenCaptureTarget; capture_system_audio?: boolean; mode: RecordingMode }
export type StereoMode = "stereo" | "monoL" | "monoR"
export type StudioRecordingMeta = { segment: SingleSegment } | { inner: MultipleSegments }
export type TimelineConfiguration = { segments: TimelineSegment[]; zoomSegments: ZoomSegment[] }
export type TimelineSegment = { recordingSegment?: number; timescale: number; start: number; end: number }
export type UploadMode = { Initial: { pre_created_video: VideoUploadInfo | null } } | "Reupload"
export type UploadProgress = { progress: number }
export type UploadResult = { Success: string } | "NotAuthenticated" | "PlanCheckFailed" | "UpgradeRequired"
export type Video = { duration: number; width: number; height: number; fps: number; start_time: number }
export type VideoMeta = { path: string; fps?: number; 
/**
 * unix time of the first frame
 */
start_time?: number | null }
export type VideoRecordingMetadata = { duration: number; size: number }
export type VideoUploadInfo = { id: string; link: string; config: S3UploadMeta }
export type XY<T> = { x: T; y: T }
export type ZoomMode = "auto" | { manual: { x: number; y: number } }
export type ZoomSegment = { start: number; end: number; amount: number; mode: ZoomMode }

/** tauri-specta globals **/

import {
	invoke as TAURI_INVOKE,
	Channel as TAURI_CHANNEL,
} from "@tauri-apps/api/core";
import * as TAURI_API_EVENT from "@tauri-apps/api/event";
import { type WebviewWindow as __WebviewWindow__ } from "@tauri-apps/api/webviewWindow";

type __EventObj__<T> = {
	listen: (
		cb: TAURI_API_EVENT.EventCallback<T>,
	) => ReturnType<typeof TAURI_API_EVENT.listen<T>>;
	once: (
		cb: TAURI_API_EVENT.EventCallback<T>,
	) => ReturnType<typeof TAURI_API_EVENT.once<T>>;
	emit: null extends T
		? (payload?: T) => ReturnType<typeof TAURI_API_EVENT.emit>
		: (payload: T) => ReturnType<typeof TAURI_API_EVENT.emit>;
};

export type Result<T, E> =
	| { status: "ok"; data: T }
	| { status: "error"; error: E };

function __makeEvents__<T extends Record<string, any>>(
	mappings: Record<keyof T, string>,
) {
	return new Proxy(
		{} as unknown as {
			[K in keyof T]: __EventObj__<T[K]> & {
				(handle: __WebviewWindow__): __EventObj__<T[K]>;
			};
		},
		{
			get: (_, event) => {
				const name = mappings[event as keyof T];

				return new Proxy((() => {}) as any, {
					apply: (_, __, [window]: [__WebviewWindow__]) => ({
						listen: (arg: any) => window.listen(name, arg),
						once: (arg: any) => window.once(name, arg),
						emit: (arg: any) => window.emit(name, arg),
					}),
					get: (_, command: keyof __EventObj__<any>) => {
						switch (command) {
							case "listen":
								return (arg: any) => TAURI_API_EVENT.listen(name, arg);
							case "once":
								return (arg: any) => TAURI_API_EVENT.once(name, arg);
							case "emit":
								return (arg: any) => TAURI_API_EVENT.emit(name, arg);
						}
					},
				});
			},
		},
	);
}
