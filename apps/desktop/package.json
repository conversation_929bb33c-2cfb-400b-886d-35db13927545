{"name": "@cap/desktop", "type": "module", "scripts": {"dev": "pnpm -w cap-setup && dotenv -e ../../.env -- pnpm run preparescript && dotenv -e ../../.env -- pnpm tauri dev", "build:tauri": "dotenv -e ../../.env -- pnpm run preparescript && dotenv -e ../../.env -- pnpm tauri build", "preparescript": "node scripts/prepare.js", "localdev": "dotenv -e ../../.env -- vinxi dev --port 3001", "build": "vinxi build", "tauri": "tauri"}, "dependencies": {"@aerofoil/rive-solid-canvas": "^2.1.4", "@cap/database": "workspace:*", "@cap/ui-solid": "workspace:*", "@cap/utils": "workspace:*", "@cap/web-api-contract": "workspace:*", "@corvu/tooltip": "^0.2.1", "@intercom/messenger-js-sdk": "^0.0.14", "@kobalte/core": "^0.13.7", "@radix-ui/colors": "^3.0.0", "@rive-app/canvas": "^2.26.7", "@solid-primitives/bounds": "^0.0.122", "@solid-primitives/context": "^0.2.3", "@solid-primitives/date": "^2.0.23", "@solid-primitives/deep": "^0.2.9", "@solid-primitives/event-bus": "^1.1.1", "@solid-primitives/event-listener": "^2.3.3", "@solid-primitives/history": "^0.1.5", "@solid-primitives/memo": "^1.4.2", "@solid-primitives/refs": "^1.0.8", "@solid-primitives/resize-observer": "^2.0.26", "@solid-primitives/scheduled": "^1.4.3", "@solid-primitives/storage": "^4.0.0", "@solid-primitives/timer": "^1.3.9", "@solid-primitives/websocket": "^1.2.2", "@solidjs/router": "^0.14.2", "@solidjs/start": "^1.1.3", "@tanstack/solid-query": "^5.51.21", "@tauri-apps/api": "2.5.0", "@tauri-apps/plugin-clipboard-manager": "^2.2.1", "@tauri-apps/plugin-deep-link": "^2.2.0", "@tauri-apps/plugin-dialog": "2.0.1", "@tauri-apps/plugin-fs": "2.2.0", "@tauri-apps/plugin-http": "^2.4.4", "@tauri-apps/plugin-notification": "2.0.0", "@tauri-apps/plugin-opener": "^2.2.6", "@tauri-apps/plugin-os": "2.0.0", "@tauri-apps/plugin-process": "2.0.0", "@tauri-apps/plugin-shell": ">=2.0.1", "@tauri-apps/plugin-store": "2.1.0", "@tauri-apps/plugin-updater": "2.0.0", "@ts-rest/core": "^3.52.1", "@types/react-tooltip": "^4.2.4", "cva": "npm:class-variance-authority@^0.7.0", "effect": "^3.7.2", "mp4box": "^0.5.2", "posthog-js": "^1.215.3", "solid-js": "^1.9.3", "solid-markdown": "^2.0.13", "solid-presence": "^0.1.8", "solid-toast": "^0.5.0", "solid-transition-group": "^0.2.3", "unplugin-auto-import": "^0.18.2", "unplugin-icons": "^0.19.2", "uuid": "^9.0.1", "vinxi": "^0.5.6", "webcodecs": "^0.1.0", "zod": "^3.24.2"}, "devDependencies": {"@fontsource/geist-sans": "^5.0.3", "@iconify/json": "^2.2.239", "@tauri-apps/cli": ">=2.1.0", "@total-typescript/ts-reset": "^0.6.1", "@types/dom-webcodecs": "^0.1.11", "@types/uuid": "^9.0.8", "cross-env": "^7.0.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.0.1", "vitest": "~2.1.9"}}