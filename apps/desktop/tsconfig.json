{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "jsxImportSource": "solid-js",
    "types": [
      "vite/client",
      "vinxi/client",
      "@cap/ui-solid/types",
      "vitest/importMeta"
    ],

    /* Linting */
    "strict": true,
    "paths": {
      "~/*": ["./src/*"]
    }
  },
  "include": ["src"]
}
