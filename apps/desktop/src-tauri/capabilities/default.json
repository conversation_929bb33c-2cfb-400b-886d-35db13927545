{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["*"], "permissions": ["fs:default", "fs:allow-resource-read", "fs:allow-resource-read-recursive", "fs:write-all", "fs:read-all", "fs:allow-appdata-read", "fs:allow-appdata-write", "fs:allow-picture-read-recursive", {"identifier": "fs:scope", "allow": [{"path": "$APPDATA/**"}, {"path": "$HOME/**"}, {"path": "$RESOURCE/**"}]}, "core:path:allow-resolve-directory", "core:path:default", "core:event:default", "core:menu:default", "core:window:default", "core:window:allow-close", "core:window:allow-destroy", "core:window:allow-hide", "core:window:allow-show", "core:window:allow-center", "core:window:allow-minimize", "core:window:allow-unminimize", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-set-size", "core:window:allow-set-focus", "core:window:allow-start-dragging", "core:window:allow-set-position", "core:window:allow-set-theme", "core:window:allow-set-progress-bar", "core:window:allow-set-effects", "core:webview:default", "core:webview:allow-create-webview-window", "core:app:allow-version", "shell:default", "core:image:default", "dialog:default", "store:default", "process:default", "oauth:allow-start", "updater:default", "notification:default", "deep-link:default", {"identifier": "http:default", "allow": [{"url": "http://*"}, {"url": "https://*"}, {"url": "http://localhost:*"}]}, "clipboard-manager:allow-write-text", "opener:allow-reveal-item-in-dir"]}