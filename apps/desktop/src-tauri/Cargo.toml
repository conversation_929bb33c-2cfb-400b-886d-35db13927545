[package]
name = "cap-desktop"
version = "0.3.57"
description = "Beautiful screen recordings, owned by you."
authors = ["you"]
edition = "2021"

[lints]
workspace = true

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "cap_desktop"
crate-type = ["lib", "cdylib", "staticlib"]

[build-dependencies]
tauri-build = { version = "2.1.0", features = [] }

[target.'cfg(target_os = "macos")'.build-dependencies]
swift-rs = { version = "1.0.6", features = ["build"] }

[dependencies]
tauri = { workspace = true, features = [
	"macos-private-api",
	"protocol-asset",
	"tray-icon",
	"image-png",
	"devtools",
] }
tauri-specta = { version = "=2.0.0-rc.20", features = ["derive", "typescript"] }
tauri-plugin-dialog = "2.2.0"
tauri-plugin-fs = "2.2.0"
tauri-plugin-global-shortcut = "2.2.0"
tauri-plugin-http = "2.2.0"
tauri-plugin-notification = "2.2.0"
tauri-plugin-os = "2.2.0"
tauri-plugin-process = "2.2.0"
tauri-plugin-shell = "2.2.0"
tauri-plugin-single-instance = { version = "2.2.0", features = ["deep-link"] }
tauri-plugin-store = "2.2.0"
tauri-plugin-updater = "2.3.1"
tauri-plugin-oauth = { git = "https://github.com/FabianLars/tauri-plugin-oauth", branch = "v2" }
tauri-plugin-window-state = "2.2.0"
tauri-plugin-positioner = "2.2.0"
tauri-plugin-deep-link = "2.2.0"
tauri-plugin-clipboard-manager = "2.2.1"
tauri-plugin-opener = "2.2.6"

serde = { version = "1", features = ["derive"] }
serde_json = "1.0.111"
specta.workspace = true
specta-typescript = "0.0.7"
tokio.workspace = true
uuid = { version = "1.10.0", features = ["v4"] }
scap.workspace = true
image = "0.25.2"
mp4 = "0.14.0"
futures-intrusive = "0.5.0"
anyhow.workspace = true
futures = "0.3"
axum = { version = "0.7.5", features = ["ws"] }
tracing.workspace = true
tempfile = "3.9.0"
ffmpeg.workspace = true
chrono = { version = "0.4.31", features = ["serde"] }
rodio = "0.19.0"
png = "0.17.13"
device_query = "2.1.0"
base64 = "0.22.1"
reqwest = { version = "0.12.7", features = ["json", "stream", "multipart"] }
dotenvy_macro = "0.15.7"
global-hotkey = "0.6.3"
rand = "0.8.5"
cpal.workspace = true
keyed_priority_queue = "0.4.2"
sentry.workspace = true
clipboard-rs = "0.2.2"
whisper-rs = "0.11.0"
lazy_static = "1.4.0"
log = "0.4.20"

cap-utils = { path = "../../../crates/utils" }
cap-project = { path = "../../../crates/project" }
cap-rendering = { path = "../../../crates/rendering" }
cap-editor = { path = "../../../crates/editor" }
cap-media = { path = "../../../crates/media" }
cap-flags = { path = "../../../crates/flags" }
cap-recording = { path = "../../../crates/recording" }
cap-export = { path = "../../../crates/export" }
cap-audio = { path = "../../../crates/audio" }
flume.workspace = true
tracing-subscriber = "0.3.19"
tracing-appender = "0.2.3"
dirs = "6.0.0"
relative-path = "1.9.3"
cap-fail = { version = "0.1.0", path = "../../../crates/fail" }
tokio-stream = { version = "0.1.17", features = ["sync"] }
md5 = "0.7.0"
tokio-util = "0.7.15"

[target.'cfg(target_os = "macos")'.dependencies]
core-graphics = "0.24.0"
core-foundation = "0.10.0"
objc2-app-kit = { version = "0.3.0", features = [
	"NSWindow",
	"NSResponder",
	"NSHapticFeedback",
] }
cocoa = "0.26.0"
objc = "0.2.7"
swift-rs = "1.0.6"
tauri-nspanel = { git = "https://github.com/ahkohd/tauri-nspanel", branch = "v2" }

[target.'cfg(target_os= "windows")'.dependencies]
windows = { workspace = true, features = [
	"Win32_Foundation",
	"Win32_System",
	"Win32_UI_WindowsAndMessaging",
	"Win32_Graphics_Gdi",
] }
windows-sys = { workspace = true }

[target.'cfg(unix)'.dependencies]
nix = { version = "0.29.0", features = ["fs"] }

# Optimize for smaller binary size
[profile.release]
panic = "abort"   # Strip expensive panic clean-up logic
codegen-units = 1 # Compile crates one after another so the compiler can optimize better
lto = true        # Enables link to optimizations
opt-level = "s"   # Optimize for binary size
debug = true
