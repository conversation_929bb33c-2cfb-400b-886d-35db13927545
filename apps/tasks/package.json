{"name": "@cap/tasks", "version": "0.3.1", "private": true, "main": "src/index.ts", "scripts": {"start": "node dist/src/index.js", "dev": "ts-node src/index.ts", "build": "tsc", "start:dist": "node dist/src/index.js", "lint": "eslint --fix src test", "test": "jest", "typecheck": "tsc --noEmit"}, "dependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.24", "@types/jest": "^29.5.12", "@types/morgan": "^1.9.9", "@types/node": "^20.12.6", "@types/supertest": "^6.0.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "fluent-ffmpeg": "^2.1.3", "helmet": "^7.1.0", "morgan": "^1.10.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.6.0", "@typescript-eslint/parser": "^7.6.0", "eslint": "^8.57.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "jest": "^29.7.0", "nodemon": "^3.1.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2"}, "engines": {"node": "20"}}