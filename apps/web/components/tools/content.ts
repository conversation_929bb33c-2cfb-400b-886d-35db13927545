import { ToolPageContent } from "@/components/tools/types";

export const webmToMp4Content: ToolPageContent = {
  slug: "webm-to-mp4",
  title: "WEBM to MP4 Converter",
  description:
    "Convert WEBM to MP4 online for free. No watermark, no registration required.",
  publishedAt: "2023-11-19",
  category: "Tool",
  author: "Cap",
  tags: ["webm to mp4", "video converter", "free", "online"],

  cta: {
    title: "Need advanced screen recording with effects?",
    description:
      "Cap is a free open-source screen recorder with editing, GIFs, and annotations.",
    buttonText: "Download Cap Free",
  },

  featuresTitle: "Why Use Our WEBM to MP4 Converter?",
  featuresDescription:
    "Our browser-based conversion happens 100% on your device, which means it's private, fast, and doesn't require an internet connection once loaded.",
  features: [
    {
      title: "Privacy-First",
      description:
        "Your videos never leave your computer. All processing happens entirely in your browser.",
    },
    {
      title: "No Quality Loss",
      description:
        "Maintain the original video quality during conversion, with no compression artifacts.",
    },
    {
      title: "Unlimited Conversions",
      description:
        "Convert as many videos as you want, with no daily limit or restrictions.",
    },
    {
      title: "No Watermarks",
      description:
        "Unlike other online converters, we don't add any watermarks to your videos.",
    },
    {
      title: "Works Without Internet",
      description:
        "Once the page is loaded, you can convert videos offline - perfect for traveling.",
    },
  ],

  faqs: [
    {
      question: "How does the online converter work?",
      answer:
        "Our converter uses WebAssembly and modern browser APIs to transcode your video directly in your browser. Your file is never uploaded to any server.",
    },
    {
      question: "What is the maximum file size?",
      answer:
        "Our converter can handle files up to 500MB, which is suitable for most screen recordings and videos.",
    },
    {
      question: "How long does conversion take?",
      answer:
        "Conversion speed depends on your device's performance and the video's size. Most conversions complete within a few minutes.",
    },
    {
      question: "Will the video quality be reduced?",
      answer:
        "No, our converter maintains the original quality of your video. However, the file size might change due to the different compression methods used by MP4.",
    },
    {
      question: "Do you support other formats?",
      answer:
        "Yes! We also support converting between MP4, WEBM, MOV, and other formats. Check out our <a href='/tools/convert'>other conversion tools</a>.",
    },
  ],
};

export const mp4ToWebmContent: ToolPageContent = {
  slug: "mp4-to-webm",
  title: "MP4 to WEBM Converter",
  description:
    "Convert MP4 to WEBM online for free. No watermark, no registration required.",
  publishedAt: "2023-11-19",
  category: "Tool",
  author: "Cap",
  tags: ["mp4 to webm", "video converter", "free", "online"],

  cta: {
    title: "Need advanced screen recording with effects?",
    description:
      "Cap is a free open-source screen recorder with editing, GIFs, and annotations.",
    buttonText: "Download Cap Free",
  },

  featuresTitle: "Why Use Our MP4 to WEBM Converter?",
  featuresDescription:
    "Our browser-based conversion happens 100% on your device, which means it's private, fast, and doesn't require an internet connection once loaded.",
  features: [
    {
      title: "Privacy-First",
      description:
        "Your videos never leave your computer. All processing happens entirely in your browser.",
    },
    {
      title: "No Quality Loss",
      description:
        "Maintain the original video quality during conversion, with no compression artifacts.",
    },
    {
      title: "Unlimited Conversions",
      description:
        "Convert as many videos as you want, with no daily limit or restrictions.",
    },
    {
      title: "No Watermarks",
      description:
        "Unlike other online converters, we don't add any watermarks to your videos.",
    },
    {
      title: "Works Without Internet",
      description:
        "Once the page is loaded, you can convert videos offline - perfect for traveling.",
    },
  ],

  faqs: [
    {
      question: "How does the online converter work?",
      answer:
        "Our converter uses WebAssembly and modern browser APIs to transcode your video directly in your browser. Your file is never uploaded to any server.",
    },
    {
      question: "What is the maximum file size?",
      answer:
        "Our converter can handle files up to 500MB, which is suitable for most screen recordings and videos.",
    },
    {
      question: "How long does conversion take?",
      answer:
        "Conversion speed depends on your device's performance and the video's size. Most conversions complete within a few minutes.",
    },
    {
      question: "Will the video quality be reduced?",
      answer:
        "No, our converter maintains the original quality of your video. However, the file size might change due to the different compression methods used by WEBM.",
    },
    {
      question: "Do you support other formats?",
      answer:
        "Yes! We also support converting between MP4, WEBM, MOV, and other formats. Check out our <a href='/tools/convert'>other conversion tools</a>.",
    },
  ],
};

export const videoSpeedControllerContent: ToolPageContent = {
  slug: "video-speed-controller",
  title: "Video Speed Controller",
  description:
    "Speed up or slow down videos online for free. No watermark, no quality loss.",
  publishedAt: "2023-11-20",
  category: "Tool",
  author: "Cap",
  tags: ["video speed control", "speed up video", "slow motion", "free", "online"],

  cta: {
    title: "Need advanced screen recording with effects?",
    description:
      "Cap is a free open-source screen recorder with editing, GIFs, and annotations.",
    buttonText: "Download Cap Free",
  },

  featuresTitle: "Why Use Our Video Speed Controller?",
  featuresDescription:
    "Our browser-based video speed controller works 100% on your device, making it private, fast, and functional without an internet connection once loaded.",
  features: [
    {
      title: "Privacy-First",
      description:
        "Your videos never leave your computer. All processing happens entirely in your browser.",
    },
    {
      title: "Multiple Speed Options",
      description:
        "Choose from 0.25x (very slow) to 3x (ultra fast) to get exactly the speed you need.",
    },
    {
      title: "Audio Preservation",
      description:
        "We maintain audio synchronization and pitch, even at different speeds.",
    },
    {
      title: "No Watermarks",
      description:
        "Unlike other online tools, we don't add any watermarks to your videos.",
    },
    {
      title: "Works Without Internet",
      description:
        "Once the page is loaded, you can process videos offline - perfect for traveling.",
    },
  ],

  faqs: [
    {
      question: "How does the video speed controller work?",
      answer:
        "Our tool uses advanced browser APIs like MediaRecorder and Canvas to adjust your video's playback speed and create a new file with the adjusted timing.",
    },
    {
      question: "What is the maximum file size?",
      answer:
        "Our tool can handle files up to 500MB, which is suitable for most videos and screen recordings.",
    },
    {
      question: "Will slowing down a video make it choppy?",
      answer:
        "For best results when slowing down, choose videos with higher frame rates (60fps is ideal). While some choppiness may occur with extreme slowdowns, our tool maintains as much smoothness as possible.",
    },
    {
      question: "Will the video quality be reduced?",
      answer:
        "No, our tool maintains the original quality of your video. However, the file size will change proportionally to the speed change.",
    },
    {
      question: "Which video formats are supported?",
      answer:
        "We support all major video formats including MP4, WEBM, MOV, AVI and MKV.",
    },
  ],
};

export const trimVideoContent: ToolPageContent = {
  slug: "trim-video-online",
  title: "Trim Video Online",
  description:
    "Cut unwanted sections from MP4, WebM, MOV and more. Instantly in your browser with zero quality loss.",
  publishedAt: "2023-11-21",
  category: "Tools",
  author: "Cap",
  tags: ["Video Trimming", "Online Tools", "Browser", "Free"],

  cta: {
    title: "The open source Loom alternative",
    description: "Cap is lightweight, powerful, and cross-platform. Record and share securely in seconds with custom S3 bucket support..",
    buttonText: "Download Cap Free",
  },

  featuresTitle: "Why Use Cap's Online Video Trimmer?",
  featuresDescription: "Our local-first trimmer does one thing stupid-fast: cut video. No sign-ups, no waiting.",
  features: [
    {
      title: "Instant & Private",
      description: "All processing happens on-device. Nothing ever touches our servers.",
    },
    {
      title: "Lossless Export",
      description: "We snip at keyframes, so quality stays identical to the original.",
    },
    {
      title: "Any Format",
      description: "MP4, WebM, MOV, MKV, GIF. If your browser plays it, we can trim it.",
    },
    {
      title: "No Watermarks",
      description: "Free means free. Your clip stays clean.",
    },
    {
      title: "Works Offline",
      description: "Once the page loads you can trim even without internet. Perfect on planes.",
    },
  ],

  faqs: [
    {
      question: "What's the maximum file size?",
      answer: "Browser memory is the only limit. Works fine up to ~500MB in Chrome.",
    },
    {
      question: "Can I trim Zoom or Loom recordings?",
      answer: "Absolutely. Export the MP4/WebM first, then drag it in here.",
    },
    {
      question: "How does the online trimmer work?",
      answer: "Our trimmer uses the MediaRecorder API to capture the portion of video you selected, then saves it as a new file. This all happens locally in your browser.",
    },
    {
      question: "Which browsers work best?",
      answer: "Chrome, Edge, and Firefox provide the best performance and compatibility. Safari has some limitations with video processing in the browser.",
    },
  ],
}; 