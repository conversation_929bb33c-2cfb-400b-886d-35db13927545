---
title: Major bug fixes/improvements + New settings
app: Cap Desktop
publishedAt: "2024-10-08"
version: 0.3.0-beta.5.4
image:
---

- New Rust powered webcam preview. Faster, better quality and more reliable
- New permissions flow, removing Camera/Microphone as required options. Now only required on request of either.
- New “Automatically open editor after recording” option in settings
- New “Sign out” button in settings
- New and improved sync engine for video/audio
- New and improved window selector (app names)
- On window record, the window will now be brought to focus
- Ordered Previous Recordings in settings by timestamp
- Ordered Previous Screenshots in settings by timestamp
- Better synchronisation in recordings
- Fixed issue where the webcam light would stay on, even when not selected
- Fixed webcam startup issue
- Fixed webcam stutter/FPS issue on record
- Fixed issue with oauth sometimes becoming invalid
- Fixed issue where some resolutions would have artifacts (specifically window recordings)
