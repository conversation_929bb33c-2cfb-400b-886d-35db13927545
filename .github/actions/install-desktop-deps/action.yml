name: Install desktop-specific dependencies
description: Installs desktop-specific dependencies for the current platform
runs:
  using: "composite"
  steps:
    - name: install dependencies (ubuntu only)
      if: ${{ runner.os == 'Linux' }}
      shell: bash
      run: |
        sudo apt update
        sudo apt install libwebkit2gtk-4.1-dev \
          build-essential \
          curl \
          wget \
          file \
          libxdo-dev \
          libssl-dev \
          libayatana-appindicator3-dev \
          librsvg2-dev \
          libpipewire-0.3-dev \
          ffmpeg clang libavcodec-dev libavformat-dev libavutil-dev libavfilter-dev libavdevice-dev pkg-config libasound2-dev
