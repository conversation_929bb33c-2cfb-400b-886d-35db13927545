name: Setup JS (pnpm & node)
description: Sets up pnpm & node with dependencies and caching
runs:
  using: "composite"
  steps:
    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 10.5.2
        run_install: false

    - name: Setup node
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: pnpm

    - name: Install frontend dependencies
      shell: bash
      run: pnpm install
