name: Setup Rust
description: Sets up rust and caching
inputs:
  save-cache:
    description: "Whether to save the cache"
    required: false
    default: "false"
  target:
    description: "Toolchain target triple"
    required: true
runs:
  using: "composite"
  steps:
    - name: Install Rust stable
      uses: dtolnay/rust-toolchain@stable
      with:
        toolchain: 1.80.0
        target: ${{ inputs.target }}
        components: clippy

    - name: Cache Rust Dependencies
      uses: Swatinem/rust-cache@v2
      with:
        save-if: ${{ inputs.save-cache }}
        shared-key: ${{ inputs.target }}

    - name: Create desktop distDir
      shell: bash
      run: mkdir -p apps/desktop/.output/public
