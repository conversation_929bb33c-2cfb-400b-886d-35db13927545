name: "publish"

# change this when ready to release if you want CI/CD
on:
  workflow_dispatch:
    inputs:
      interactionId:
        description: "Discord Interaction ID"
        required: false
        type: string

env:
  CN_APPLICATION: cap/cap
  APP_CARGO_TOML: apps/desktop/src-tauri/Cargo.toml
  SENTRY_ORG: cap-s2
  SENTRY_PROJECT: cap-desktop

jobs:
  draft:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.read_version.outputs.value }}
      needs_release: ${{ steps.create_tag.outputs.tag_existed != 'true' }}
      cn_release_stdout: ${{ steps.create_cn_release.outputs.stdout }}
      gh_release_url: ${{ steps.create_gh_release.outputs.url }}
    permissions:
      contents: write
      id-token: write
    steps:
      - uses: actions/checkout@v4

      - name: Read version number
        uses: SebRollen/toml-action@v1.0.2
        id: read_version
        with:
          file: ${{ env.APP_CARGO_TOML }}
          field: "package.version"

      - name: Create tag
        id: create_tag
        if: ${{ steps.create_tag.outputs.tag_existed != 'true' }}
        uses: actions/github-script@v7
        with:
          script: |
            const tag = "cap-v${{ steps.read_version.outputs.value }}";
            const tagRef = `tags/${tag}`;

            const TAG_EXISTED = "tag_existed";
            const TAG_NAME = "tag_name";

            core.setOutput(TAG_NAME, tag);

            async function main() {
              let tagExisted = true;

              try {
                await github.rest.git.getRef({
                  ref: tagRef,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                });

                tagExisted = true;
                core.notice(`Release skipped as tag '${tag}' already exists. Update the version in '${{ env.APP_CARGO_TOML }}' before starting another release.`);
              } catch (error) {
                if ("status" in error && error.status === 404) tagExisted = false;
                else throw error;
              }

             	core.setOutput(TAG_EXISTED, tagExisted);

              if (!tagExisted)
                await github.rest.git.createRef({
                  ref: `refs/${tagRef}`,
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  sha: context.sha,
                });
            }

            main();

      - name: Create draft CN release
        id: create_cn_release
        uses: crabnebula-dev/cloud-release@v0
        with:
          command: release draft ${{ env.CN_APPLICATION }} ${{ steps.read_version.outputs.value }} --framework tauri
          api-key: ${{ secrets.CN_API_KEY }}

      - name: Create draft GH release
        id: create_gh_release
        # TODO: Change to stable version when available
        uses: softprops/action-gh-release@v2
        with:
          name: ${{ steps.read_version.outputs.value }}
          tag_name: ${{ steps.create_tag.outputs.tag_name }}
          draft: true
          generate_release_notes: true

      - name: Update Discord interaction
        if: ${{ inputs.interactionId != '' }}
        uses: actions/github-script@v7
        with:
          script: |
            async function main() {
              const token = await core.getIDToken("cap-discord-bot");
              const cnReleaseId = JSON.parse(`${{ steps.create_cn_release.outputs.stdout }}`).id;

              const resp = await fetch("https://cap-discord-bot.brendonovich.workers.dev/github-workflow", {
                method: "POST",
                body: JSON.stringify({
                  type: "release-ready",
                  tag: "${{ steps.create_tag.outputs.tag_name }}",
                  version: "${{ steps.read_version.outputs.value }}",
                  releaseUrl: "${{ steps.create_gh_release.outputs.url }}",
                  interactionId: "${{ inputs.interactionId }}",
                  cnReleaseId
                }),
                headers: {
                	"Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                }
              });

              if(resp.status !== 200) throw new Error(await resp.text());
            }

            main();

  build:
    needs: draft
    if: ${{ needs.draft.outputs.needs_release == 'true' }}
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        settings:
          - target: x86_64-apple-darwin
            runner: macos-latest
          - target: aarch64-apple-darwin
            runner: macos-latest
          - target: x86_64-pc-windows-msvc
            runner: windows-latest-l
    runs-on: ${{ matrix.settings.runner }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Create API Key File
        run: echo "${{ secrets.APPLE_API_KEY_FILE }}" > api.p8

      - uses: apple-actions/import-codesign-certs@v2
        if: ${{ matrix.settings.runner == 'macos-latest' }}
        with:
          p12-file-base64: ${{ secrets.APPLE_CERTIFICATE }}
          p12-password: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}

      - name: Verify certificate
        if: ${{ matrix.settings.runner == 'macos-latest' }}
        run: security find-identity -v -p codesigning ${{ runner.temp }}/build.keychain

      - name: Rust setup
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.settings.target }}

      - name: Rust cache
        uses: swatinem/rust-cache@v2
        with:
          shared-key: ${{ matrix.settings.target }}

      - uses: ./.github/actions/setup-js

      - name: Create .env file in root
        run: |
          echo "appVersion=${{ needs.draft.outputs.version }}" >> .env
          echo "VITE_ENVIRONMENT=production" >> .env
          echo "CAP_DESKTOP_SENTRY_URL=https://<EMAIL>/4508330917101568" >> .env
          echo "NEXT_PUBLIC_WEB_URL=${{ secrets.NEXT_PUBLIC_WEB_URL }}" >> .env
          echo 'NEXTAUTH_URL=${{ secrets.NEXT_PUBLIC_WEB_URL }}' >> .env
          echo 'VITE_POSTHOG_KEY=${{ secrets.VITE_POSTHOG_KEY }}' >> .env
          echo 'VITE_POSTHOG_HOST=${{ secrets.VITE_POSTHOG_HOST }}' >> .env
          echo 'VITE_SERVER_URL=${{ secrets.NEXT_PUBLIC_WEB_URL }}' >> .env
          echo "NEXT_PUBLIC_CAP_AWS_REGION=${{ secrets.NEXT_PUBLIC_CAP_AWS_REGION }}" >> .env
          echo "NEXT_PUBLIC_CAP_AWS_BUCKET=${{ secrets.NEXT_PUBLIC_CAP_AWS_BUCKET }}" >> .env
          echo 'RUST_TARGET_TRIPLE=${{ matrix.settings.target }}' >> .env

      - name: Build app
        working-directory: apps/desktop
        run: |
          pnpm -w cap-setup
          pnpm build:tauri --target ${{ matrix.settings.target }} --config src-tauri/tauri.prod.conf.json
        env:
          # https://github.com/tauri-apps/tauri-action/issues/740
          CI: false
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # codesigning
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          APPLE_SIGNING_IDENTITY: ${{ secrets.APPLE_SIGNING_IDENTITY }}
          # notarization
          APPLE_API_ISSUER: ${{ secrets.APPLE_API_ISSUER }}
          APPLE_API_KEY: ${{ secrets.APPLE_API_KEY }}
          APPLE_API_KEY_PATH: ${{ github.workspace }}/api.p8
          APPLE_KEYCHAIN: ${{ runner.temp }}/build.keychain
          TAURI_SIGNING_PRIVATE_KEY: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY }}
          TAURI_SIGNING_PRIVATE_KEY_PASSWORD: ${{ secrets.TAURI_SIGNING_PRIVATE_KEY_PASSWORD }}

      - name: Upload assets
        uses: crabnebula-dev/cloud-release@v0
        with:
          command: release upload ${{ env.CN_APPLICATION }} "${{ needs.draft.outputs.version }}" --framework tauri
          api-key: ${{ secrets.CN_API_KEY }}

      - name: Upload debug symbols to Sentry
        if: ${{ matrix.settings.runner == 'macos-latest' }}
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
        working-directory: target
        run: |
          curl -sL https://sentry.io/get-cli/ | bash
          sentry-cli debug-files upload -o ${{ env.SENTRY_ORG }} -p ${{ env.SENTRY_PROJECT }} Cap.dSYM

  done:
    needs: [draft, build]
    runs-on: ubuntu-latest
    permissions:
      contents: write
      id-token: write
    steps:
      - name: Send Discord notification
        if: ${{ inputs.interactionId != '' }}
        uses: actions/github-script@v7
        with:
          script: |
            async function main() {
              const token = await core.getIDToken("cap-discord-bot");
              const cnReleaseId = JSON.parse(`${{ needs.draft.outputs.cn_release_stdout }}`).id;

              const resp = await fetch("https://cap-discord-bot.brendonovich.workers.dev/github-workflow", {
                method: "POST",
                body: JSON.stringify({
                  type: "release-done",
                  interactionId: "${{ inputs.interactionId }}",
                  version: "${{ needs.draft.outputs.version }}",
                  releaseUrl: "${{ needs.draft.outputs.gh_release_url }}",
                  cnReleaseId
                }),
                headers: {
                	"Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                }
              });

              if(resp.status !== 200) throw new Error(await resp.text());
            }

            main();
