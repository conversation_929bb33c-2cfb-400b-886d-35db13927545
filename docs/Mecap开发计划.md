# Mecap 开发计划

本文档基于 **MoSCoW** 原则（Must-have, Should-have, Could-have, Won't-have）对 Mecap 项目的特性进行优先级排序，并制定分阶段的开发计划。旨在确保核心功能优先交付，并以迭代的方式逐步完善产品。

---

## 优先级划分 (MoSCoW Prioritization)

### 🟢 Must-have (必须有) - MVP 核心

这些是构成产品最小可行版本（MVP）的基础，没有它们，产品无法发布。

- **M1**: **PC 端核心截图功能**：支持在 Windows 和 macOS 上进行**区域截图**和**窗口截图**。
- **M2**: **基础截图编辑器**：截图后能立即打开编辑器，支持**裁剪**、**矩形**、**箭头**、**文字**标注。
- **M3**: **本地存储**：截图可以保存到用户本地文件系统。
- **M4**: **基础 UI**：提供一个简洁的界面来触发截图和查看最近的截图列表。
- **M5**: **项目基础架构**：搭建基于 Tauri + Vite + React + TypeScript 的项目框架。

### 🟡 Should-have (应该有) - 核心体验增强

这些功能非常重要，能显著提升用户体验，但并非 MVP 的必要条件。应在 MVP 发布后尽快开发。

- **S1**: **用户认证体系**：集成 Firebase Auth，支持用户注册和登录。
- **S2**: **手动云同步**：用户可以手动将本地截图上传/同步到 Firebase Storage。
- **S3**: **截图画廊**：提供一个专门的页面来管理（查看、删除）所有已同步的截图。
- **S4**: **PC 平台原生集成**：
  - Windows: 系统托盘图标及右键菜单。
  - macOS: 菜单栏图标及菜单。
- **S5**: **基础设置**：允许用户自定义全局截图快捷键和默认保存路径。
- **S6**: **高级编辑器功能（V1）**：增加**模糊/马赛克**工具，用于隐藏敏感信息。

### 🔵 Could-have (可以有) - 扩展功能

这些是“锦上添花”的功能，可以在产品稳定、核心需求满足后考虑，用于增强产品竞争力。

- **C1**: **移动端支持 (Android/iOS)**：开发移动端应用，实现屏幕截图和查看同步的截图。
- **C2**: **自动同步**：截图保存后自动在后台上传，无需手动操作。
- **C3**: **截图内容识别 (OCR)**：使用客户端或云端 OCR 技术识别截图中的文字。
- **C4**: **内容搜索**：基于 OCR 结果，允许用户搜索截图内容。
- **C5**: **标签与组织**：支持用户为截图打标签、创建文件夹进行管理。
- **C6**: **无缝分享**：生成分享链接，或直接发送到其他应用。

### 🔴 Won't-have (这次不会有) - 范围之外

为了保持专注，以下功能在可预见的未来版本中**不予考虑**。

- **W1**: **屏幕录制**。
- **W2**: **与项目管理工具（Trello 等）的深度集成**。
- **W3**: **团队协作功能**。

---

## 开发阶段计划 (Development Phases)

### **Phase 1: MVP - 核心功能实现 (Focus: Must-have)**

**目标**：发布一个稳定、可用的 PC 端截图工具。

| 任务 ID | 任务描述 | 关联特性 | 状态 |
| :--- | :--- | :--- | :--- |
| P1-T1 | 初始化项目，配置 Tauri, Vite, React, TS, Material-UI | M5 | To Do |
| P1-T2 | **后端**: 开发 Rust 模块，实现 Windows/macOS 的区域和窗口截图 | M1 | To Do |
| P1-T3 | **前端**: 创建截图触发界面和截图预览界面 | M1, M4 | To Do |
| P1-T4 | **前端**: 开发截图编辑器画布，实现裁剪、矩形、箭头、文字工具 | M2 | To Do |
| P1-T5 | **后端**: 实现将编辑后的截图保存到本地的功能 | M3 | To Do |
| P1-T6 | **前端**: 构建一个简单的最近截图列表 | M4 | To Do |
| P1-T7 | 在 Windows 和 macOS 上进行核心功能测试和打包 | M1-M4 | To Do |

### **Phase 2: 体验增强与云同步 (Focus: Should-have)**

**目标**：引入云同步，完善 PC 端用户体验。

| 任务 ID | 任务描述 | 关联特性 | 状态 |
| :--- | :--- | :--- | :--- |
| P2-T1 | 集成 Firebase SDK，实现邮箱/Google 登录 | S1 | To Do |
| P2-T2 | **后端**: 编写与 Firebase Storage 交互的接口（上传/下载） | S2 | To Do |
| P2-T3 | **前端**: 开发截图画廊页面，展示云端截图 | S3 | To Do |
| P2-T4 | **前端**: 在界面添加“手动同步”按钮和同步状态提示 | S2 | To Do |
| P2-T5 | **后端**: 实现系统托盘（Win）和菜单栏（macOS）功能 | S4 | To Do |
| P2-T6 | **前端**: 开发设置页面，允许用户配置快捷键和保存路径 | S5 | To Do |
| P2-T7 | **前端**: 在编辑器中添加模糊/马赛克工具 | S6 | To Do |

### **Phase 3: 智能化与扩展 (Focus: Could-have)**

**目标**：探索高级功能，并开始移动端布局。

| 任务 ID | 任务描述 | 关联特性 | 状态 |
| :--- | :--- | :--- | :--- |
| P3-T1 | **研究**: 调研并选择 OCR 技术方案（如 Tesseract.js） | C3 | To Do |
| P3-T2 | 实现截图 OCR 功能，并将识别的文本存入元数据 | C3 | To Do |
| P3-T3 | 开发基于文本内容的搜索功能 | C4 | To Do |
| P3-T4 | **研究**: 调研 Tauri 移动端开发，创建 Android/iOS 项目 | C1 | To Do |
| P3-T5 | **移动端**: 实现原生截图插件，并能查看同步的截图 | C1 | To Do |
| P3-T6 | 实现截图自动同步机制 | C2 | To Do |
| P3-T7 | 实现标签管理和分享功能 | C5, C6 | To Do |