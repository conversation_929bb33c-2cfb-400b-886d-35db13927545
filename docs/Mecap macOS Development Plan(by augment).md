# Mecap macOS Development Plan

Based on your project design documents, here's a focused development roadmap for the macOS version of Mecap.

## 1. Development Roadmap for macOS

### Phase 1: Core Screenshot Functionality (2-3 weeks)
- **Milestone 1.1: Project Setup & Basic UI**
  - Task 1: Configure macOS-specific Tauri settings
  - Task 2: Create basic React UI with Material-UI
  - Task 3: Implement macOS menu bar integration

- **Milestone 1.2: Screenshot Capture**
  - Task 1: Implement region selection mechanism
  - Task 2: Implement window selection mechanism
  - Task 3: Add keyboard shortcuts for screenshot actions

- **Milestone 1.3: Basic Editor**
  - Task 1: Create canvas for screenshot editing
  - Task 2: Implement rectangle, arrow, and text annotation tools
  - Task 3: Add crop functionality

### Phase 2: Storage & Enhancement (2 weeks)
- **Milestone 2.1: Local Storage**
  - Task 1: Implement screenshot saving to local filesystem
  - Task 2: Create screenshot gallery/history view
  - Task 3: Add export options (PNG, JPG, PDF)

- **Milestone 2.2: macOS Integration**
  - Task 1: Add system tray/menu bar icon
  - Task 2: Implement macOS notifications
  - Task 3: Add startup on login option

### Phase 3: Cloud Sync & Polish (2-3 weeks)
- **Milestone 3.1: User Authentication**
  - Task 1: Implement Firebase Auth integration
  - Task 2: Create user profile management

- **Milestone 3.2: Cloud Sync**
  - Task 1: Implement Firebase Storage integration
  - Task 2: Add manual sync functionality
  - Task 3: Implement sync status indicators

- **Milestone 3.3: Final Polish**
  - Task 1: Optimize performance
  - Task 2: Add macOS-specific UI refinements
  - Task 3: Conduct user testing and fix issues

## 2. Technical Recommendations for macOS Screenshot Implementation

For macOS screenshot functionality, I recommend using a combination of Tauri's Rust backend capabilities and macOS-specific APIs:

````rust path=src-tauri/src/screenshot.rs mode=EDIT
use std::path::PathBuf;
use serde::{Deserialize, Serialize};
use tauri::{command, Runtime, Window};

#[cfg(target_os = "macos")]
use cocoa::{
    base::{selector, nil},
    foundation::{NSRect, NSPoint, NSSize},
};

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotArea {
    x: f64,
    y: f64,
    width: f64,
    height: f64,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct ScreenshotResult {
    path: String,
    width: u32,
    height: u32,
}

#[command]
pub fn capture_region(area: ScreenshotArea, save_path: Option<String>) -> Result<ScreenshotResult, String> {
    #[cfg(target_os = "macos")]
    {
        // Implementation using macOS CGDisplay APIs
        let path = save_screenshot_to_file(area, save_path)?;
        Ok(ScreenshotResult {
            path,
            width: area.width as u32,
            height: area.height as u32,
        })
    }
    
    #[cfg(not(target_os = "macos"))]
    {
        Err("Not implemented for this platform".into())
    }
}

#[cfg(target_os = "macos")]
fn save_screenshot_to_file(area: ScreenshotArea, save_path: Option<String>) -> Result<String, String> {
    // Implementation using Core Graphics to capture screen region
    // and save to file
    // ...
    
    Ok(save_path.unwrap_or_else(|| generate_default_path()))
}

fn generate_default_path() -> String {
    let timestamp = chrono::Local::now().format("%Y%m%d_%H%M%S");
    format!("~/Pictures/Mecap/screenshot_{}.png", timestamp)
}
````

For the React frontend, create a component to trigger and display screenshots:

````typescript path=src/components/ScreenshotCapture.tsx mode=EDIT
import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';
import { Button, CircularProgress, Box } from '@mui/material';

interface ScreenshotResult {
  path: string;
  width: number;
  height: number;
}

export const ScreenshotCapture: React.FC = () => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [screenshot, setScreenshot] = useState<ScreenshotResult | null>(null);

  const captureRegion = async () => {
    setIsCapturing(true);
    try {
      // Hide the app window during capture
      // Show selection overlay
      // After selection is made:
      const result = await invoke<ScreenshotResult>('capture_region', {
        area: { x: 0, y: 0, width: 800, height: 600 },
        savePath: null
      });
      setScreenshot(result);
    } catch (error) {
      console.error('Screenshot failed:', error);
    } finally {
      setIsCapturing(false);
    }
  };

  return (
    <Box sx={{ p: 2 }}>
      <Button 
        variant="contained" 
        onClick={captureRegion}
        disabled={isCapturing}
      >
        {isCapturing ? <CircularProgress size={24} /> : 'Capture Region'}
      </Button>
      
      {screenshot && (
        <Box sx={{ mt: 2 }}>
          <img 
            src={`file://${screenshot.path}`} 
            alt="Screenshot" 
            style={{ maxWidth: '100%' }}
          />
        </Box>
      )}
    </Box>
  );
}
````

## 3. Architecture Improvements

Based on your existing architecture, I suggest these improvements:

1. **Modular Plugin System**: Create a plugin architecture for screenshot tools to make it easier to add new annotation types.

2. **Reactive State Management**: Consider using a more robust state management solution like Zustand (as mentioned in your docs) with persistence:

````typescript path=src/store/screenshotStore.ts mode=EDIT
import create from 'zustand';
import { persist } from 'zustand/middleware';

interface Screenshot {
  id: string;
  path: string;
  createdAt: number;
  tags: string[];
  syncStatus: 'local' | 'syncing' | 'synced' | 'error';
}

interface ScreenshotState {
  screenshots: Screenshot[];
  recentScreenshots: Screenshot[];
  addScreenshot: (path: string) => void;
  deleteScreenshot: (id: string) => void;
  updateSyncStatus: (id: string, status: Screenshot['syncStatus']) => void;
}

export const useScreenshotStore = create<ScreenshotState>()(
  persist(
    (set, get) => ({
      screenshots: [],
      recentScreenshots: [],
      
      addScreenshot: (path: string) => {
        const newScreenshot: Screenshot = {
          id: Date.now().toString(),
          path,
          createdAt: Date.now(),
          tags: [],
          syncStatus: 'local',
        };
        
        set(state => ({
          screenshots: [newScreenshot, ...state.screenshots],
          recentScreenshots: [newScreenshot, ...state.recentScreenshots].slice(0, 5),
        }));
      },
      
      deleteScreenshot: (id: string) => {
        set(state => ({
          screenshots: state.screenshots.filter(s => s.id !== id),
          recentScreenshots: state.recentScreenshots.filter(s => s.id !== id),
        }));
      },
      
      updateSyncStatus: (id: string, status: Screenshot['syncStatus']) => {
        set(state => ({
          screenshots: state.screenshots.map(s => 
            s.id === id ? { ...s, syncStatus: status } : s
          ),
          recentScreenshots: state.recentScreenshots.map(s => 
            s.id === id ? { ...s, syncStatus: status } : s
          ),
        }));
      },
    }),
    {
      name: 'mecap-screenshots',
    }
  )
);
````

3. **Separation of Concerns**: Clearly separate the screenshot capture, editing, and storage logic into distinct modules.

## 4. macOS-Specific Considerations

1. **Permissions**: macOS requires screen recording permissions for screenshot functionality. You'll need to:
   - Add the `NSScreenCaptureUsageDescription` key to your `Info.plist`
   - Request permissions at runtime

2. **Sandboxing**: If you plan to distribute through the Mac App Store, you'll need to handle sandboxing restrictions.

3. **Menu Bar Integration**: macOS users expect screenshot tools to be accessible from the menu bar.

4. **Keyboard Shortcuts**: Consider using standard macOS screenshot shortcuts (⌘+Shift+3, ⌘+Shift+4) or alternatives that don't conflict.

5. **Retina Display Support**: Ensure your app handles high-DPI displays correctly.

## 5. Recommended Tauri Plugins and Libraries

1. **tauri-plugin-store**: For persistent settings
   ```bash
   pnpm add @tauri-apps/plugin-store
   ```

2. **tauri-plugin-fs**: For file system operations
   ```bash
   pnpm add @tauri-apps/plugin-fs
   ```

3. **tauri-plugin-clipboard-manager**: For copying screenshots to clipboard
   ```bash
   pnpm add @tauri-apps/plugin-clipboard-manager
   ```

4. **tauri-plugin-notification**: For notification when screenshots are taken
   ```bash
   pnpm add @tauri-apps/plugin-notification
   ```

5. **tauri-plugin-os**: For OS-specific functionality
   ```bash
   pnpm add @tauri-apps/plugin-os
   ```

For the Rust side, consider these crates:
- `screenshots` crate for cross-platform screenshot capabilities
- `image` crate for image processing
- `cocoa` and `core-graphics` for macOS-specific APIs

To integrate these with your Tauri app, update your `src-tauri/src/lib.rs`:

````rust path=src-tauri/src/lib.rs mode=EDIT
mod screenshot;

use tauri::Runtime;
use tauri_plugin_store::StorePlugin;
use tauri_plugin_clipboard_manager::ClipboardManagerPlugin;
use tauri_plugin_notification::NotificationPlugin;
use tauri_plugin_os::OsPlugin;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(StorePlugin::default())
        .plugin(ClipboardManagerPlugin::default())
        .plugin(NotificationPlugin::default())
        .plugin(OsPlugin::default())
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![
            screenshot::capture_region,
            // Add other commands here
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
````

This plan provides a solid foundation for developing the macOS version of Mecap, focusing on the core screenshot functionality first and then expanding to more advanced features.
