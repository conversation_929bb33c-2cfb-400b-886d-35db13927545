<!-- Generated by SpecStory -->

# 分析录屏技术方案与接口实现 (2025-07-06 22:46:00)

_**User**_

请分析当前项目，使用什么技术方案来实现选择录屏的‘Screen’和Window'两种方式，而且使用什么接口实现点击‘Window’后能获取当前PC的不同程序页面进行跳转录屏。比如用的Tauri哪些接口等，帮我把实现方案输出到markdown文件

---

_**Assistant**_

<details>
            <summary>Searched codebase "录屏 Screen Window 选择实现方案，以及如何获取当前PC不同程序窗口列表" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `apps/desktop/src-tauri/src/recording.rs` | L235-260 |
| `apps/desktop/src-tauri/src/windows.rs` | L391-430 |
| `apps/desktop/src-tauri/src/windows.rs` | L577-605 |
| `apps/desktop/src/routes/(window-chrome)/(main).tsx` | L354-413 |
| `apps/desktop/src/utils/queries.ts` | L1-33 |
| `apps/desktop/src-tauri/src/windows.rs` | L430-473 |
| `apps/desktop/src/routes/(window-chrome)/(main).tsx` | L127-186 |
| `apps/desktop/src-tauri/src/windows.rs` | L94-152 |
| `apps/desktop/src-tauri/src/windows.rs` | L152-181 |
| `apps/desktop/src-tauri/src/windows.rs` | L361-391 |
| `apps/desktop/src-tauri/src/recording.rs` | L298-342 |
| `apps/desktop/src/routes/(window-chrome)/(main).tsx` | L520-567 |
| `apps/desktop/src/routes/(window-chrome)/(main).tsx` | L58-127 |
| `apps/desktop/src-tauri/src/lib.rs` | L317-355 |
| `apps/desktop/src-tauri/src/windows.rs` | L73-94 |
| `apps/desktop/src/routes/(window-chrome)/(main).tsx` | L312-354 |
| `apps/desktop/src/routes/(window-chrome)/(main).tsx` | L1-50 |
| `apps/desktop/src/utils/tauri.ts` | L34-37 |
| `apps/desktop/src/utils/tauri.ts` | L375-376 |
| `apps/desktop/src-tauri/src/fake_window.rs` | L43-101 |
| `apps/desktop/src/routes/window-capture-occluder.tsx` | L1-57 |
| `apps/desktop/src-tauri/src/windows.rs` | L473-494 |
| `apps/desktop/src/components/titlebar/controls/CaptionControlsWindows11.tsx` | L1-30 |
| `apps/desktop/src-tauri/src/windows.rs` | L1-41 |
| `apps/desktop/src/routes/capture-area.tsx` | L55-91 |

</details>

---

<details>
            <summary>Searched codebase "Tauri 如何获取所有可录制的窗口（Window）列表，涉及哪些接口或Rust代码" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `apps/desktop/src-tauri/src/recording.rs` | L119-196 |
| `apps/desktop/src-tauri/src/lib.rs` | L317-355 |
| `apps/desktop/src-tauri/src/recording.rs` | L235-260 |
| `apps/desktop/src-tauri/src/lib.rs` | L1739-1794 |
| `apps/desktop/src-tauri/src/windows.rs` | L473-494 |
| `apps/desktop/src-tauri/src/windows.rs` | L577-605 |
| `apps/desktop/src-tauri/src/lib.rs` | L1888-1915 |
| `apps/desktop/src-tauri/src/lib.rs` | L748-800 |
| `apps/desktop/src-tauri/src/windows.rs` | L94-152 |
| `apps/desktop/src-tauri/src/windows.rs` | L430-473 |
| `apps/desktop/src-tauri/src/lib.rs` | L242-317 |
| `apps/desktop/src-tauri/src/windows.rs` | L73-94 |
| `apps/desktop/src-tauri/src/windows.rs` | L1-41 |
| `apps/desktop/src-tauri/src/lib.rs` | L133-184 |
| `apps/desktop/src-tauri/src/windows.rs` | L361-391 |
| `apps/desktop/src-tauri/src/lib.rs` | L1593-1656 |
| `apps/desktop/src-tauri/src/lib.rs` | L1374-1420 |
| `apps/desktop/src-tauri/src/lib.rs` | L1-74 |
| `apps/desktop/src-tauri/src/windows.rs` | L391-430 |
| `apps/desktop/src-tauri/src/windows.rs` | L152-181 |
| `apps/desktop/src-tauri/src/lib.rs` | L2009-2049 |
| `apps/desktop/src-tauri/src/recording.rs` | L298-342 |
| `apps/desktop/src-tauri/src/lib.rs` | L2118-2188 |
| `apps/desktop/src-tauri/src/lib.rs` | L858-929 |
| `apps/desktop/src-tauri/src/windows.rs` | L41-73 |

</details>

---

<details>
            <summary>Searched codebase "Screen/Window 录屏模式切换的前端实现方式，涉及哪些组件和状态管理" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `apps/desktop/src/components/ModeSelect.tsx` | L45-91 |
| `apps/desktop/src/components/Mode.tsx` | L1-42 |
| `apps/desktop/src/components/titlebar/controls/CaptionControlsWindows11.tsx` | L1-30 |
| `apps/desktop/src/components/ModeSelect.tsx` | L1-19 |
| `apps/desktop/src/components/titlebar/controls/CaptionControlsWindows11.tsx` | L30-80 |
| `apps/desktop/src/components/titlebar/Titlebar.tsx` | L43-62 |
| `apps/desktop/src/components/Mode.tsx` | L42-97 |
| `apps/desktop/src/components/titlebar/controls/CaptionControlsWindows11.tsx` | L80-94 |
| `apps/desktop/src/components/titlebar/Titlebar.tsx` | L1-43 |
| `apps/desktop/src/components/titlebar/controls/WindowControlButton.tsx` | L1-14 |
| `apps/desktop/src/components/titlebar/controls/CaptionControlsWindows11.tsx` | L94-112 |
| `apps/desktop/src/components/Cropper.tsx` | L408-442 |
| `apps/desktop/src/components/Cropper.tsx` | L598-642 |
| `apps/desktop/src/components/titlebar/controls/CaptionControlsWindows11.tsx` | L141-163 |
| `apps/desktop/src/components/Cropper.tsx` | L1-26 |
| `apps/desktop/src/components/titlebar/controls/CaptionControlsWindows11.tsx` | L112-128 |
| `apps/desktop/src/components/Cropper.tsx` | L642-690 |
| `apps/desktop/src/components/ModeSelect.tsx` | L19-45 |
| `apps/desktop/src/components/titlebar/controls/CaptionControlsWindows11.tsx` | L128-141 |
| `apps/desktop/src/components/Cropper.tsx` | L200-236 |
| `apps/desktop/src/components/Cropper.tsx` | L505-538 |
| `apps/desktop/src/components/CropAreaRenderer.tsx` | L212-282 |
| `apps/desktop/src/components/Cropper.tsx` | L538-598 |
| `apps/desktop/src/components/Cropper.tsx` | L131-200 |
| `apps/desktop/src/components/Cropper.tsx` | L236-270 |

</details>

---

Read file: apps/desktop/src/utils/tauri.ts

---

Read file: apps/desktop/src-tauri/src/recording.rs

---

Read file: apps/desktop/src-tauri/src/lib.rs

---

根据你的需求，以下是对项目中“Screen/Window 录屏模式选择”及“获取当前PC不同程序窗口进行跳转录屏”的技术方案分析，并整理成 markdown 文档内容：

---

# Cap 桌面端录屏模式（Screen/Window）技术实现方案

## 1. 前端实现

### 1.1 录屏模式选择（Screen/Window）

- 前端 UI 通过 `TargetSelect` 组件（见 `apps/desktop/src/routes/(window-chrome)/(main).tsx`）实现录屏目标的选择，包括“Screen”和“Window”两种模式。
- 录屏目标的状态管理通过 `rawOptions.captureTarget`，其结构如下：
  ```ts
  type ScreenCaptureTarget =
    | { variant: "window"; id: number }
    | { variant: "screen"; id: number }
    | { variant: "area"; screen: number; bounds: Bounds }
  ```
- 选择“Screen”时，调用 `listScreens` 获取所有可用屏幕；选择“Window”时，调用 `listWindows` 获取所有可用窗口。
- 相关 UI 组件和状态管理主要分布在：
  - `apps/desktop/src/routes/(window-chrome)/(main).tsx`
  - `apps/desktop/src/components/Mode.tsx`、`ModeSelect.tsx`（录制模式切换

---

